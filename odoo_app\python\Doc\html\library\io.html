<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="io — Core tools for working with streams" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/io.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/io.py Overview: The io module provides Python’s main facilities for dealing with various types of I/O. There are three main types of I/O: text I/O, binary I/O and raw I/O. These ar..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/io.py Overview: The io module provides Python’s main facilities for dealing with various types of I/O. There are three main types of I/O: text I/O, binary I/O and raw I/O. These ar..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>io — Core tools for working with streams &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="time — Time access and conversions" href="time.html" />
    <link rel="prev" title="os — Miscellaneous operating system interfaces" href="os.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/io.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">io</span></code> — Core tools for working with streams</a><ul>
<li><a class="reference internal" href="#overview">Overview</a><ul>
<li><a class="reference internal" href="#text-i-o">Text I/O</a></li>
<li><a class="reference internal" href="#binary-i-o">Binary I/O</a></li>
<li><a class="reference internal" href="#raw-i-o">Raw I/O</a></li>
</ul>
</li>
<li><a class="reference internal" href="#text-encoding">Text Encoding</a><ul>
<li><a class="reference internal" href="#opt-in-encodingwarning">Opt-in EncodingWarning</a></li>
</ul>
</li>
<li><a class="reference internal" href="#high-level-module-interface">High-level Module Interface</a></li>
<li><a class="reference internal" href="#class-hierarchy">Class hierarchy</a><ul>
<li><a class="reference internal" href="#i-o-base-classes">I/O Base Classes</a></li>
<li><a class="reference internal" href="#raw-file-i-o">Raw File I/O</a></li>
<li><a class="reference internal" href="#buffered-streams">Buffered Streams</a></li>
<li><a class="reference internal" href="#id1">Text I/O</a></li>
</ul>
</li>
<li><a class="reference internal" href="#performance">Performance</a><ul>
<li><a class="reference internal" href="#id2">Binary I/O</a></li>
<li><a class="reference internal" href="#id3">Text I/O</a></li>
<li><a class="reference internal" href="#multi-threading">Multi-threading</a></li>
<li><a class="reference internal" href="#reentrancy">Reentrancy</a></li>
</ul>
</li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="os.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">os</span></code> — Miscellaneous operating system interfaces</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="time.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">time</span></code> — Time access and conversions</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/io.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="time.html" title="time — Time access and conversions"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="os.html" title="os — Miscellaneous operating system interfaces"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="allos.html" accesskey="U">Generic Operating System Services</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">io</span></code> — Core tools for working with streams</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-io">
<span id="io-core-tools-for-working-with-streams"></span><h1><a class="reference internal" href="#module-io" title="io: Core tools for working with streams."><code class="xref py py-mod docutils literal notranslate"><span class="pre">io</span></code></a> — Core tools for working with streams<a class="headerlink" href="#module-io" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/io.py">Lib/io.py</a></p>
<hr class="docutils" />
<section id="overview">
<span id="io-overview"></span><h2>Overview<a class="headerlink" href="#overview" title="Link to this heading">¶</a></h2>
<p id="index-0">The <a class="reference internal" href="#module-io" title="io: Core tools for working with streams."><code class="xref py py-mod docutils literal notranslate"><span class="pre">io</span></code></a> module provides Python’s main facilities for dealing with various
types of I/O.  There are three main types of I/O: <em>text I/O</em>, <em>binary I/O</em>
and <em>raw I/O</em>.  These are generic categories, and various backing stores can
be used for each of them.  A concrete object belonging to any of these
categories is called a <a class="reference internal" href="../glossary.html#term-file-object"><span class="xref std std-term">file object</span></a>.  Other common terms are <em>stream</em>
and <em>file-like object</em>.</p>
<p>Independent of its category, each concrete stream object will also have
various capabilities: it can be read-only, write-only, or read-write. It can
also allow arbitrary random access (seeking forwards or backwards to any
location), or only sequential access (for example in the case of a socket or
pipe).</p>
<p>All streams are careful about the type of data you give to them.  For example
giving a <a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a> object to the <code class="xref py py-meth docutils literal notranslate"><span class="pre">write()</span></code> method of a binary stream
will raise a <a class="reference internal" href="exceptions.html#TypeError" title="TypeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">TypeError</span></code></a>.  So will giving a <a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a> object to the
<code class="xref py py-meth docutils literal notranslate"><span class="pre">write()</span></code> method of a text stream.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>Operations that used to raise <a class="reference internal" href="exceptions.html#IOError" title="IOError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">IOError</span></code></a> now raise <a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a>, since
<a class="reference internal" href="exceptions.html#IOError" title="IOError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">IOError</span></code></a> is now an alias of <a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a>.</p>
</div>
<section id="text-i-o">
<h3>Text I/O<a class="headerlink" href="#text-i-o" title="Link to this heading">¶</a></h3>
<p>Text I/O expects and produces <a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a> objects.  This means that whenever
the backing store is natively made of bytes (such as in the case of a file),
encoding and decoding of data is made transparently as well as optional
translation of platform-specific newline characters.</p>
<p>The easiest way to create a text stream is with <a class="reference internal" href="functions.html#open" title="open"><code class="xref py py-meth docutils literal notranslate"><span class="pre">open()</span></code></a>, optionally
specifying an encoding:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">f</span> <span class="o">=</span> <span class="nb">open</span><span class="p">(</span><span class="s2">&quot;myfile.txt&quot;</span><span class="p">,</span> <span class="s2">&quot;r&quot;</span><span class="p">,</span> <span class="n">encoding</span><span class="o">=</span><span class="s2">&quot;utf-8&quot;</span><span class="p">)</span>
</pre></div>
</div>
<p>In-memory text streams are also available as <a class="reference internal" href="#io.StringIO" title="io.StringIO"><code class="xref py py-class docutils literal notranslate"><span class="pre">StringIO</span></code></a> objects:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">f</span> <span class="o">=</span> <span class="n">io</span><span class="o">.</span><span class="n">StringIO</span><span class="p">(</span><span class="s2">&quot;some initial text data&quot;</span><span class="p">)</span>
</pre></div>
</div>
<p>The text stream API is described in detail in the documentation of
<a class="reference internal" href="#io.TextIOBase" title="io.TextIOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">TextIOBase</span></code></a>.</p>
</section>
<section id="binary-i-o">
<h3>Binary I/O<a class="headerlink" href="#binary-i-o" title="Link to this heading">¶</a></h3>
<p>Binary I/O (also called <em>buffered I/O</em>) expects
<a class="reference internal" href="../glossary.html#term-bytes-like-object"><span class="xref std std-term">bytes-like objects</span></a> and produces <a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a>
objects.  No encoding, decoding, or newline translation is performed.  This
category of streams can be used for all kinds of non-text data, and also when
manual control over the handling of text data is desired.</p>
<p>The easiest way to create a binary stream is with <a class="reference internal" href="functions.html#open" title="open"><code class="xref py py-meth docutils literal notranslate"><span class="pre">open()</span></code></a> with <code class="docutils literal notranslate"><span class="pre">'b'</span></code> in
the mode string:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">f</span> <span class="o">=</span> <span class="nb">open</span><span class="p">(</span><span class="s2">&quot;myfile.jpg&quot;</span><span class="p">,</span> <span class="s2">&quot;rb&quot;</span><span class="p">)</span>
</pre></div>
</div>
<p>In-memory binary streams are also available as <a class="reference internal" href="#io.BytesIO" title="io.BytesIO"><code class="xref py py-class docutils literal notranslate"><span class="pre">BytesIO</span></code></a> objects:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">f</span> <span class="o">=</span> <span class="n">io</span><span class="o">.</span><span class="n">BytesIO</span><span class="p">(</span><span class="sa">b</span><span class="s2">&quot;some initial binary data: </span><span class="se">\x00\x01</span><span class="s2">&quot;</span><span class="p">)</span>
</pre></div>
</div>
<p>The binary stream API is described in detail in the docs of
<a class="reference internal" href="#io.BufferedIOBase" title="io.BufferedIOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">BufferedIOBase</span></code></a>.</p>
<p>Other library modules may provide additional ways to create text or binary
streams.  See <a class="reference internal" href="socket.html#socket.socket.makefile" title="socket.socket.makefile"><code class="xref py py-meth docutils literal notranslate"><span class="pre">socket.socket.makefile()</span></code></a> for example.</p>
</section>
<section id="raw-i-o">
<h3>Raw I/O<a class="headerlink" href="#raw-i-o" title="Link to this heading">¶</a></h3>
<p>Raw I/O (also called <em>unbuffered I/O</em>) is generally used as a low-level
building-block for binary and text streams; it is rarely useful to directly
manipulate a raw stream from user code.  Nevertheless, you can create a raw
stream by opening a file in binary mode with buffering disabled:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">f</span> <span class="o">=</span> <span class="nb">open</span><span class="p">(</span><span class="s2">&quot;myfile.jpg&quot;</span><span class="p">,</span> <span class="s2">&quot;rb&quot;</span><span class="p">,</span> <span class="n">buffering</span><span class="o">=</span><span class="mi">0</span><span class="p">)</span>
</pre></div>
</div>
<p>The raw stream API is described in detail in the docs of <a class="reference internal" href="#io.RawIOBase" title="io.RawIOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">RawIOBase</span></code></a>.</p>
</section>
</section>
<section id="text-encoding">
<span id="io-text-encoding"></span><h2>Text Encoding<a class="headerlink" href="#text-encoding" title="Link to this heading">¶</a></h2>
<p>The default encoding of <a class="reference internal" href="#io.TextIOWrapper" title="io.TextIOWrapper"><code class="xref py py-class docutils literal notranslate"><span class="pre">TextIOWrapper</span></code></a> and <a class="reference internal" href="functions.html#open" title="open"><code class="xref py py-func docutils literal notranslate"><span class="pre">open()</span></code></a> is
locale-specific (<a class="reference internal" href="locale.html#locale.getencoding" title="locale.getencoding"><code class="xref py py-func docutils literal notranslate"><span class="pre">locale.getencoding()</span></code></a>).</p>
<p>However, many developers forget to specify the encoding when opening text files
encoded in UTF-8 (e.g. JSON, TOML, Markdown, etc…) since most Unix
platforms use UTF-8 locale by default. This causes bugs because the locale
encoding is not UTF-8 for most Windows users. For example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="c1"># May not work on Windows when non-ASCII characters in the file.</span>
<span class="k">with</span> <span class="nb">open</span><span class="p">(</span><span class="s2">&quot;README.md&quot;</span><span class="p">)</span> <span class="k">as</span> <span class="n">f</span><span class="p">:</span>
    <span class="n">long_description</span> <span class="o">=</span> <span class="n">f</span><span class="o">.</span><span class="n">read</span><span class="p">()</span>
</pre></div>
</div>
<p>Accordingly, it is highly recommended that you specify the encoding
explicitly when opening text files. If you want to use UTF-8, pass
<code class="docutils literal notranslate"><span class="pre">encoding=&quot;utf-8&quot;</span></code>. To use the current locale encoding,
<code class="docutils literal notranslate"><span class="pre">encoding=&quot;locale&quot;</span></code> is supported since Python 3.10.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<dl class="simple">
<dt><a class="reference internal" href="os.html#utf8-mode"><span class="std std-ref">Python UTF-8 Mode</span></a></dt><dd><p>Python UTF-8 Mode can be used to change the default encoding to
UTF-8 from locale-specific encoding.</p>
</dd>
<dt><span class="target" id="index-1"></span><a class="pep reference external" href="https://peps.python.org/pep-0686/"><strong>PEP 686</strong></a></dt><dd><p>Python 3.15 will make <a class="reference internal" href="os.html#utf8-mode"><span class="std std-ref">Python UTF-8 Mode</span></a> default.</p>
</dd>
</dl>
</div>
<section id="opt-in-encodingwarning">
<span id="io-encoding-warning"></span><h3>Opt-in EncodingWarning<a class="headerlink" href="#opt-in-encodingwarning" title="Link to this heading">¶</a></h3>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.10: </span>See <span class="target" id="index-2"></span><a class="pep reference external" href="https://peps.python.org/pep-0597/"><strong>PEP 597</strong></a> for more details.</p>
</div>
<p>To find where the default locale encoding is used, you can enable
the <a class="reference internal" href="../using/cmdline.html#cmdoption-X"><code class="xref std std-option docutils literal notranslate"><span class="pre">-X</span> <span class="pre">warn_default_encoding</span></code></a> command line option or set the
<span class="target" id="index-3"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONWARNDEFAULTENCODING"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONWARNDEFAULTENCODING</span></code></a> environment variable, which will
emit an <a class="reference internal" href="exceptions.html#EncodingWarning" title="EncodingWarning"><code class="xref py py-exc docutils literal notranslate"><span class="pre">EncodingWarning</span></code></a> when the default encoding is used.</p>
<p>If you are providing an API that uses <a class="reference internal" href="functions.html#open" title="open"><code class="xref py py-func docutils literal notranslate"><span class="pre">open()</span></code></a> or
<a class="reference internal" href="#io.TextIOWrapper" title="io.TextIOWrapper"><code class="xref py py-class docutils literal notranslate"><span class="pre">TextIOWrapper</span></code></a> and passes <code class="docutils literal notranslate"><span class="pre">encoding=None</span></code> as a parameter, you
can use <a class="reference internal" href="#io.text_encoding" title="io.text_encoding"><code class="xref py py-func docutils literal notranslate"><span class="pre">text_encoding()</span></code></a> so that callers of the API will emit an
<a class="reference internal" href="exceptions.html#EncodingWarning" title="EncodingWarning"><code class="xref py py-exc docutils literal notranslate"><span class="pre">EncodingWarning</span></code></a> if they don’t pass an <code class="docutils literal notranslate"><span class="pre">encoding</span></code>. However,
please consider using UTF-8 by default (i.e. <code class="docutils literal notranslate"><span class="pre">encoding=&quot;utf-8&quot;</span></code>) for
new APIs.</p>
</section>
</section>
<section id="high-level-module-interface">
<h2>High-level Module Interface<a class="headerlink" href="#high-level-module-interface" title="Link to this heading">¶</a></h2>
<dl class="py data">
<dt class="sig sig-object py" id="io.DEFAULT_BUFFER_SIZE">
<span class="sig-prename descclassname"><span class="pre">io.</span></span><span class="sig-name descname"><span class="pre">DEFAULT_BUFFER_SIZE</span></span><a class="headerlink" href="#io.DEFAULT_BUFFER_SIZE" title="Link to this definition">¶</a></dt>
<dd><p>An int containing the default buffer size used by the module’s buffered I/O
classes.  <a class="reference internal" href="functions.html#open" title="open"><code class="xref py py-func docutils literal notranslate"><span class="pre">open()</span></code></a> uses the file’s blksize (as obtained by
<a class="reference internal" href="os.html#os.stat" title="os.stat"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.stat()</span></code></a>) if possible.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="io.open">
<span class="sig-prename descclassname"><span class="pre">io.</span></span><span class="sig-name descname"><span class="pre">open</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">file</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">mode</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'r'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">buffering</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">-1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">encoding</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">errors</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">newline</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">closefd</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">opener</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#io.open" title="Link to this definition">¶</a></dt>
<dd><p>This is an alias for the builtin <a class="reference internal" href="functions.html#open" title="open"><code class="xref py py-func docutils literal notranslate"><span class="pre">open()</span></code></a> function.</p>
<p class="audit-hook"><p>This function raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">open</span></code> with
arguments <em>path</em>, <em>mode</em> and <em>flags</em>. The <em>mode</em> and <em>flags</em>
arguments may have been modified or inferred from the original call.</p>
</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="io.open_code">
<span class="sig-prename descclassname"><span class="pre">io.</span></span><span class="sig-name descname"><span class="pre">open_code</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">path</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#io.open_code" title="Link to this definition">¶</a></dt>
<dd><p>Opens the provided file with mode <code class="docutils literal notranslate"><span class="pre">'rb'</span></code>. This function should be used
when the intent is to treat the contents as executable code.</p>
<p><em>path</em> should be a <a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a> and an absolute path.</p>
<p>The behavior of this function may be overridden by an earlier call to the
<a class="reference internal" href="../c-api/file.html#c.PyFile_SetOpenCodeHook" title="PyFile_SetOpenCodeHook"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyFile_SetOpenCodeHook()</span></code></a>. However, assuming that <em>path</em> is a
<a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a> and an absolute path, <code class="docutils literal notranslate"><span class="pre">open_code(path)</span></code> should always behave
the same as <code class="docutils literal notranslate"><span class="pre">open(path,</span> <span class="pre">'rb')</span></code>. Overriding the behavior is intended for
additional validation or preprocessing of the file.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.8.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="io.text_encoding">
<span class="sig-prename descclassname"><span class="pre">io.</span></span><span class="sig-name descname"><span class="pre">text_encoding</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">encoding</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">stacklevel</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">2</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#io.text_encoding" title="Link to this definition">¶</a></dt>
<dd><p>This is a helper function for callables that use <a class="reference internal" href="functions.html#open" title="open"><code class="xref py py-func docutils literal notranslate"><span class="pre">open()</span></code></a> or
<a class="reference internal" href="#io.TextIOWrapper" title="io.TextIOWrapper"><code class="xref py py-class docutils literal notranslate"><span class="pre">TextIOWrapper</span></code></a> and have an <code class="docutils literal notranslate"><span class="pre">encoding=None</span></code> parameter.</p>
<p>This function returns <em>encoding</em> if it is not <code class="docutils literal notranslate"><span class="pre">None</span></code>.
Otherwise, it returns <code class="docutils literal notranslate"><span class="pre">&quot;locale&quot;</span></code> or <code class="docutils literal notranslate"><span class="pre">&quot;utf-8&quot;</span></code> depending on
<a class="reference internal" href="os.html#utf8-mode"><span class="std std-ref">UTF-8 Mode</span></a>.</p>
<p>This function emits an <a class="reference internal" href="exceptions.html#EncodingWarning" title="EncodingWarning"><code class="xref py py-class docutils literal notranslate"><span class="pre">EncodingWarning</span></code></a> if
<a class="reference internal" href="sys.html#sys.flags" title="sys.flags"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.flags.warn_default_encoding</span></code></a> is true and <em>encoding</em>
is <code class="docutils literal notranslate"><span class="pre">None</span></code>. <em>stacklevel</em> specifies where the warning is emitted.
For example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">read_text</span><span class="p">(</span><span class="n">path</span><span class="p">,</span> <span class="n">encoding</span><span class="o">=</span><span class="kc">None</span><span class="p">):</span>
    <span class="n">encoding</span> <span class="o">=</span> <span class="n">io</span><span class="o">.</span><span class="n">text_encoding</span><span class="p">(</span><span class="n">encoding</span><span class="p">)</span>  <span class="c1"># stacklevel=2</span>
    <span class="k">with</span> <span class="nb">open</span><span class="p">(</span><span class="n">path</span><span class="p">,</span> <span class="n">encoding</span><span class="p">)</span> <span class="k">as</span> <span class="n">f</span><span class="p">:</span>
        <span class="k">return</span> <span class="n">f</span><span class="o">.</span><span class="n">read</span><span class="p">()</span>
</pre></div>
</div>
<p>In this example, an <a class="reference internal" href="exceptions.html#EncodingWarning" title="EncodingWarning"><code class="xref py py-class docutils literal notranslate"><span class="pre">EncodingWarning</span></code></a> is emitted for the caller of
<code class="docutils literal notranslate"><span class="pre">read_text()</span></code>.</p>
<p>See <a class="reference internal" href="#io-text-encoding"><span class="std std-ref">Text Encoding</span></a> for more information.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.10.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.11: </span><a class="reference internal" href="#io.text_encoding" title="io.text_encoding"><code class="xref py py-func docutils literal notranslate"><span class="pre">text_encoding()</span></code></a> returns “utf-8” when UTF-8 mode is enabled and
<em>encoding</em> is <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
</div>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="io.BlockingIOError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">io.</span></span><span class="sig-name descname"><span class="pre">BlockingIOError</span></span><a class="headerlink" href="#io.BlockingIOError" title="Link to this definition">¶</a></dt>
<dd><p>This is a compatibility alias for the builtin <a class="reference internal" href="exceptions.html#BlockingIOError" title="BlockingIOError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">BlockingIOError</span></code></a>
exception.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="io.UnsupportedOperation">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">io.</span></span><span class="sig-name descname"><span class="pre">UnsupportedOperation</span></span><a class="headerlink" href="#io.UnsupportedOperation" title="Link to this definition">¶</a></dt>
<dd><p>An exception inheriting <a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a> and <a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> that is raised
when an unsupported operation is called on a stream.</p>
</dd></dl>

<div class="admonition seealso">
<p class="admonition-title">See also</p>
<dl class="simple">
<dt><a class="reference internal" href="sys.html#module-sys" title="sys: Access system-specific parameters and functions."><code class="xref py py-mod docutils literal notranslate"><span class="pre">sys</span></code></a></dt><dd><p>contains the standard IO streams: <a class="reference internal" href="sys.html#sys.stdin" title="sys.stdin"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.stdin</span></code></a>, <a class="reference internal" href="sys.html#sys.stdout" title="sys.stdout"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.stdout</span></code></a>,
and <a class="reference internal" href="sys.html#sys.stderr" title="sys.stderr"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.stderr</span></code></a>.</p>
</dd>
</dl>
</div>
</section>
<section id="class-hierarchy">
<h2>Class hierarchy<a class="headerlink" href="#class-hierarchy" title="Link to this heading">¶</a></h2>
<p>The implementation of I/O streams is organized as a hierarchy of classes.  First
<a class="reference internal" href="../glossary.html#term-abstract-base-class"><span class="xref std std-term">abstract base classes</span></a> (ABCs), which are used to
specify the various categories of streams, then concrete classes providing the
standard stream implementations.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The abstract base classes also provide default implementations of some
methods in order to help implementation of concrete stream classes.  For
example, <a class="reference internal" href="#io.BufferedIOBase" title="io.BufferedIOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">BufferedIOBase</span></code></a> provides unoptimized implementations of
<code class="xref py py-meth docutils literal notranslate"><span class="pre">readinto()</span></code> and <code class="xref py py-meth docutils literal notranslate"><span class="pre">readline()</span></code>.</p>
</div>
<p>At the top of the I/O hierarchy is the abstract base class <a class="reference internal" href="#io.IOBase" title="io.IOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">IOBase</span></code></a>.  It
defines the basic interface to a stream.  Note, however, that there is no
separation between reading and writing to streams; implementations are allowed
to raise <a class="reference internal" href="#io.UnsupportedOperation" title="io.UnsupportedOperation"><code class="xref py py-exc docutils literal notranslate"><span class="pre">UnsupportedOperation</span></code></a> if they do not support a given operation.</p>
<p>The <a class="reference internal" href="#io.RawIOBase" title="io.RawIOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">RawIOBase</span></code></a> ABC extends <a class="reference internal" href="#io.IOBase" title="io.IOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">IOBase</span></code></a>.  It deals with the reading
and writing of bytes to a stream.  <a class="reference internal" href="#io.FileIO" title="io.FileIO"><code class="xref py py-class docutils literal notranslate"><span class="pre">FileIO</span></code></a> subclasses <a class="reference internal" href="#io.RawIOBase" title="io.RawIOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">RawIOBase</span></code></a>
to provide an interface to files in the machine’s file system.</p>
<p>The <a class="reference internal" href="#io.BufferedIOBase" title="io.BufferedIOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">BufferedIOBase</span></code></a> ABC extends <a class="reference internal" href="#io.IOBase" title="io.IOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">IOBase</span></code></a>.  It deals with
buffering on a raw binary stream (<a class="reference internal" href="#io.RawIOBase" title="io.RawIOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">RawIOBase</span></code></a>).  Its subclasses,
<a class="reference internal" href="#io.BufferedWriter" title="io.BufferedWriter"><code class="xref py py-class docutils literal notranslate"><span class="pre">BufferedWriter</span></code></a>, <a class="reference internal" href="#io.BufferedReader" title="io.BufferedReader"><code class="xref py py-class docutils literal notranslate"><span class="pre">BufferedReader</span></code></a>, and <a class="reference internal" href="#io.BufferedRWPair" title="io.BufferedRWPair"><code class="xref py py-class docutils literal notranslate"><span class="pre">BufferedRWPair</span></code></a>
buffer raw binary streams that are writable, readable, and both readable and writable,
respectively. <a class="reference internal" href="#io.BufferedRandom" title="io.BufferedRandom"><code class="xref py py-class docutils literal notranslate"><span class="pre">BufferedRandom</span></code></a> provides a buffered interface to seekable streams.
Another <a class="reference internal" href="#io.BufferedIOBase" title="io.BufferedIOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">BufferedIOBase</span></code></a> subclass, <a class="reference internal" href="#io.BytesIO" title="io.BytesIO"><code class="xref py py-class docutils literal notranslate"><span class="pre">BytesIO</span></code></a>, is a stream of
in-memory bytes.</p>
<p>The <a class="reference internal" href="#io.TextIOBase" title="io.TextIOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">TextIOBase</span></code></a> ABC extends <a class="reference internal" href="#io.IOBase" title="io.IOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">IOBase</span></code></a>.  It deals with
streams whose bytes represent text, and handles encoding and decoding to and
from strings.  <a class="reference internal" href="#io.TextIOWrapper" title="io.TextIOWrapper"><code class="xref py py-class docutils literal notranslate"><span class="pre">TextIOWrapper</span></code></a>, which extends <a class="reference internal" href="#io.TextIOBase" title="io.TextIOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">TextIOBase</span></code></a>, is a buffered text
interface to a buffered raw stream (<a class="reference internal" href="#io.BufferedIOBase" title="io.BufferedIOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">BufferedIOBase</span></code></a>).  Finally,
<a class="reference internal" href="#io.StringIO" title="io.StringIO"><code class="xref py py-class docutils literal notranslate"><span class="pre">StringIO</span></code></a> is an in-memory stream for text.</p>
<p>Argument names are not part of the specification, and only the arguments of
<a class="reference internal" href="functions.html#open" title="open"><code class="xref py py-func docutils literal notranslate"><span class="pre">open()</span></code></a> are intended to be used as keyword arguments.</p>
<p>The following table summarizes the ABCs provided by the <a class="reference internal" href="#module-io" title="io: Core tools for working with streams."><code class="xref py py-mod docutils literal notranslate"><span class="pre">io</span></code></a> module:</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>ABC</p></th>
<th class="head"><p>Inherits</p></th>
<th class="head"><p>Stub Methods</p></th>
<th class="head"><p>Mixin Methods and Properties</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><a class="reference internal" href="#io.IOBase" title="io.IOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">IOBase</span></code></a></p></td>
<td></td>
<td><p><code class="docutils literal notranslate"><span class="pre">fileno</span></code>, <code class="docutils literal notranslate"><span class="pre">seek</span></code>,
and <code class="docutils literal notranslate"><span class="pre">truncate</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">close</span></code>, <code class="docutils literal notranslate"><span class="pre">closed</span></code>, <code class="docutils literal notranslate"><span class="pre">__enter__</span></code>,
<code class="docutils literal notranslate"><span class="pre">__exit__</span></code>, <code class="docutils literal notranslate"><span class="pre">flush</span></code>, <code class="docutils literal notranslate"><span class="pre">isatty</span></code>, <code class="docutils literal notranslate"><span class="pre">__iter__</span></code>,
<code class="docutils literal notranslate"><span class="pre">__next__</span></code>, <code class="docutils literal notranslate"><span class="pre">readable</span></code>, <code class="docutils literal notranslate"><span class="pre">readline</span></code>,
<code class="docutils literal notranslate"><span class="pre">readlines</span></code>, <code class="docutils literal notranslate"><span class="pre">seekable</span></code>, <code class="docutils literal notranslate"><span class="pre">tell</span></code>,
<code class="docutils literal notranslate"><span class="pre">writable</span></code>, and <code class="docutils literal notranslate"><span class="pre">writelines</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#io.RawIOBase" title="io.RawIOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">RawIOBase</span></code></a></p></td>
<td><p><a class="reference internal" href="#io.IOBase" title="io.IOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">IOBase</span></code></a></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">readinto</span></code> and
<code class="docutils literal notranslate"><span class="pre">write</span></code></p></td>
<td><p>Inherited <a class="reference internal" href="#io.IOBase" title="io.IOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">IOBase</span></code></a> methods, <code class="docutils literal notranslate"><span class="pre">read</span></code>,
and <code class="docutils literal notranslate"><span class="pre">readall</span></code></p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#io.BufferedIOBase" title="io.BufferedIOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">BufferedIOBase</span></code></a></p></td>
<td><p><a class="reference internal" href="#io.IOBase" title="io.IOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">IOBase</span></code></a></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">detach</span></code>, <code class="docutils literal notranslate"><span class="pre">read</span></code>,
<code class="docutils literal notranslate"><span class="pre">read1</span></code>, and <code class="docutils literal notranslate"><span class="pre">write</span></code></p></td>
<td><p>Inherited <a class="reference internal" href="#io.IOBase" title="io.IOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">IOBase</span></code></a> methods, <code class="docutils literal notranslate"><span class="pre">readinto</span></code>,
and <code class="docutils literal notranslate"><span class="pre">readinto1</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#io.TextIOBase" title="io.TextIOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">TextIOBase</span></code></a></p></td>
<td><p><a class="reference internal" href="#io.IOBase" title="io.IOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">IOBase</span></code></a></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">detach</span></code>, <code class="docutils literal notranslate"><span class="pre">read</span></code>,
<code class="docutils literal notranslate"><span class="pre">readline</span></code>, and
<code class="docutils literal notranslate"><span class="pre">write</span></code></p></td>
<td><p>Inherited <a class="reference internal" href="#io.IOBase" title="io.IOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">IOBase</span></code></a> methods, <code class="docutils literal notranslate"><span class="pre">encoding</span></code>,
<code class="docutils literal notranslate"><span class="pre">errors</span></code>, and <code class="docutils literal notranslate"><span class="pre">newlines</span></code></p></td>
</tr>
</tbody>
</table>
<section id="i-o-base-classes">
<h3>I/O Base Classes<a class="headerlink" href="#i-o-base-classes" title="Link to this heading">¶</a></h3>
<dl class="py class">
<dt class="sig sig-object py" id="io.IOBase">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">io.</span></span><span class="sig-name descname"><span class="pre">IOBase</span></span><a class="headerlink" href="#io.IOBase" title="Link to this definition">¶</a></dt>
<dd><p>The abstract base class for all I/O classes.</p>
<p>This class provides empty abstract implementations for many methods
that derived classes can override selectively; the default
implementations represent a file that cannot be read, written or
seeked.</p>
<p>Even though <a class="reference internal" href="#io.IOBase" title="io.IOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">IOBase</span></code></a> does not declare <code class="xref py py-meth docutils literal notranslate"><span class="pre">read()</span></code>
or <code class="xref py py-meth docutils literal notranslate"><span class="pre">write()</span></code> because their signatures will vary, implementations and
clients should consider those methods part of the interface.  Also,
implementations may raise a <a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> (or <a class="reference internal" href="#io.UnsupportedOperation" title="io.UnsupportedOperation"><code class="xref py py-exc docutils literal notranslate"><span class="pre">UnsupportedOperation</span></code></a>)
when operations they do not support are called.</p>
<p>The basic type used for binary data read from or written to a file is
<a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a>.  Other <a class="reference internal" href="../glossary.html#term-bytes-like-object"><span class="xref std std-term">bytes-like objects</span></a> are
accepted as method arguments too.  Text I/O classes work with <a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a> data.</p>
<p>Note that calling any method (even inquiries) on a closed stream is
undefined.  Implementations may raise <a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> in this case.</p>
<p><a class="reference internal" href="#io.IOBase" title="io.IOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">IOBase</span></code></a> (and its subclasses) supports the iterator protocol, meaning
that an <a class="reference internal" href="#io.IOBase" title="io.IOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">IOBase</span></code></a> object can be iterated over yielding the lines in a
stream.  Lines are defined slightly differently depending on whether the
stream is a binary stream (yielding bytes), or a text stream (yielding
character strings).  See <a class="reference internal" href="#io.IOBase.readline" title="io.IOBase.readline"><code class="xref py py-meth docutils literal notranslate"><span class="pre">readline()</span></code></a> below.</p>
<p><a class="reference internal" href="#io.IOBase" title="io.IOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">IOBase</span></code></a> is also a context manager and therefore supports the
<a class="reference internal" href="../reference/compound_stmts.html#with"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">with</span></code></a> statement.  In this example, <em>file</em> is closed after the
<code class="xref std std-keyword docutils literal notranslate"><span class="pre">with</span></code> statement’s suite is finished—even if an exception occurs:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">with</span> <span class="nb">open</span><span class="p">(</span><span class="s1">&#39;spam.txt&#39;</span><span class="p">,</span> <span class="s1">&#39;w&#39;</span><span class="p">)</span> <span class="k">as</span> <span class="n">file</span><span class="p">:</span>
    <span class="n">file</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s1">&#39;Spam and eggs!&#39;</span><span class="p">)</span>
</pre></div>
</div>
<p><a class="reference internal" href="#io.IOBase" title="io.IOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">IOBase</span></code></a> provides these data attributes and methods:</p>
<dl class="py method">
<dt class="sig sig-object py" id="io.IOBase.close">
<span class="sig-name descname"><span class="pre">close</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#io.IOBase.close" title="Link to this definition">¶</a></dt>
<dd><p>Flush and close this stream. This method has no effect if the file is
already closed. Once the file is closed, any operation on the file
(e.g. reading or writing) will raise a <a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a>.</p>
<p>As a convenience, it is allowed to call this method more than once;
only the first call, however, will have an effect.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="io.IOBase.closed">
<span class="sig-name descname"><span class="pre">closed</span></span><a class="headerlink" href="#io.IOBase.closed" title="Link to this definition">¶</a></dt>
<dd><p><code class="docutils literal notranslate"><span class="pre">True</span></code> if the stream is closed.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="io.IOBase.fileno">
<span class="sig-name descname"><span class="pre">fileno</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#io.IOBase.fileno" title="Link to this definition">¶</a></dt>
<dd><p>Return the underlying file descriptor (an integer) of the stream if it
exists.  An <a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a> is raised if the IO object does not use a file
descriptor.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="io.IOBase.flush">
<span class="sig-name descname"><span class="pre">flush</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#io.IOBase.flush" title="Link to this definition">¶</a></dt>
<dd><p>Flush the write buffers of the stream if applicable.  This does nothing
for read-only and non-blocking streams.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="io.IOBase.isatty">
<span class="sig-name descname"><span class="pre">isatty</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#io.IOBase.isatty" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if the stream is interactive (i.e., connected to
a terminal/tty device).</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="io.IOBase.readable">
<span class="sig-name descname"><span class="pre">readable</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#io.IOBase.readable" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if the stream can be read from.
If <code class="docutils literal notranslate"><span class="pre">False</span></code>, <code class="xref py py-meth docutils literal notranslate"><span class="pre">read()</span></code> will raise <a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="io.IOBase.readline">
<span class="sig-name descname"><span class="pre">readline</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">size</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">-1</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#io.IOBase.readline" title="Link to this definition">¶</a></dt>
<dd><p>Read and return one line from the stream.  If <em>size</em> is specified, at
most <em>size</em> bytes will be read.</p>
<p>The line terminator is always <code class="docutils literal notranslate"><span class="pre">b'\n'</span></code> for binary files; for text files,
the <em>newline</em> argument to <a class="reference internal" href="functions.html#open" title="open"><code class="xref py py-func docutils literal notranslate"><span class="pre">open()</span></code></a> can be used to select the line
terminator(s) recognized.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="io.IOBase.readlines">
<span class="sig-name descname"><span class="pre">readlines</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">hint</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">-1</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#io.IOBase.readlines" title="Link to this definition">¶</a></dt>
<dd><p>Read and return a list of lines from the stream.  <em>hint</em> can be specified
to control the number of lines read: no more lines will be read if the
total size (in bytes/characters) of all lines so far exceeds <em>hint</em>.</p>
<p><em>hint</em> values of <code class="docutils literal notranslate"><span class="pre">0</span></code> or less, as well as <code class="docutils literal notranslate"><span class="pre">None</span></code>, are treated as no
hint.</p>
<p>Note that it’s already possible to iterate on file objects using <code class="docutils literal notranslate"><span class="pre">for</span>
<span class="pre">line</span> <span class="pre">in</span> <span class="pre">file:</span> <span class="pre">...</span></code> without calling <code class="xref py py-meth docutils literal notranslate"><span class="pre">file.readlines()</span></code>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="io.IOBase.seek">
<span class="sig-name descname"><span class="pre">seek</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">offset</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">whence</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">os.SEEK_SET</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#io.IOBase.seek" title="Link to this definition">¶</a></dt>
<dd><p>Change the stream position to the given byte <em>offset</em>,
interpreted relative to the position indicated by <em>whence</em>,
and return the new absolute position.
Values for <em>whence</em> are:</p>
<ul class="simple">
<li><p><a class="reference internal" href="os.html#os.SEEK_SET" title="os.SEEK_SET"><code class="xref py py-data docutils literal notranslate"><span class="pre">os.SEEK_SET</span></code></a> or <code class="docutils literal notranslate"><span class="pre">0</span></code> – start of the stream (the default);
<em>offset</em> should be zero or positive</p></li>
<li><p><a class="reference internal" href="os.html#os.SEEK_CUR" title="os.SEEK_CUR"><code class="xref py py-data docutils literal notranslate"><span class="pre">os.SEEK_CUR</span></code></a> or <code class="docutils literal notranslate"><span class="pre">1</span></code> – current stream position;
<em>offset</em> may be negative</p></li>
<li><p><a class="reference internal" href="os.html#os.SEEK_END" title="os.SEEK_END"><code class="xref py py-data docutils literal notranslate"><span class="pre">os.SEEK_END</span></code></a> or <code class="docutils literal notranslate"><span class="pre">2</span></code> – end of the stream;
<em>offset</em> is usually negative</p></li>
</ul>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.1: </span>The <code class="xref py py-data docutils literal notranslate"><span class="pre">SEEK_*</span></code> constants.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3: </span>Some operating systems could support additional values, like
<a class="reference internal" href="os.html#os.SEEK_HOLE" title="os.SEEK_HOLE"><code class="xref py py-const docutils literal notranslate"><span class="pre">os.SEEK_HOLE</span></code></a> or <a class="reference internal" href="os.html#os.SEEK_DATA" title="os.SEEK_DATA"><code class="xref py py-const docutils literal notranslate"><span class="pre">os.SEEK_DATA</span></code></a>. The valid values
for a file could depend on it being open in text or binary mode.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="io.IOBase.seekable">
<span class="sig-name descname"><span class="pre">seekable</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#io.IOBase.seekable" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if the stream supports random access.  If <code class="docutils literal notranslate"><span class="pre">False</span></code>,
<a class="reference internal" href="#io.IOBase.seek" title="io.IOBase.seek"><code class="xref py py-meth docutils literal notranslate"><span class="pre">seek()</span></code></a>, <a class="reference internal" href="#io.IOBase.tell" title="io.IOBase.tell"><code class="xref py py-meth docutils literal notranslate"><span class="pre">tell()</span></code></a> and <a class="reference internal" href="#io.IOBase.truncate" title="io.IOBase.truncate"><code class="xref py py-meth docutils literal notranslate"><span class="pre">truncate()</span></code></a> will raise <a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="io.IOBase.tell">
<span class="sig-name descname"><span class="pre">tell</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#io.IOBase.tell" title="Link to this definition">¶</a></dt>
<dd><p>Return the current stream position.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="io.IOBase.truncate">
<span class="sig-name descname"><span class="pre">truncate</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">size</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#io.IOBase.truncate" title="Link to this definition">¶</a></dt>
<dd><p>Resize the stream to the given <em>size</em> in bytes (or the current position
if <em>size</em> is not specified).  The current stream position isn’t changed.
This resizing can extend or reduce the current file size.  In case of
extension, the contents of the new file area depend on the platform
(on most systems, additional bytes are zero-filled).  The new file size
is returned.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>Windows will now zero-fill files when extending.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="io.IOBase.writable">
<span class="sig-name descname"><span class="pre">writable</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#io.IOBase.writable" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if the stream supports writing.  If <code class="docutils literal notranslate"><span class="pre">False</span></code>,
<code class="xref py py-meth docutils literal notranslate"><span class="pre">write()</span></code> and <a class="reference internal" href="#io.IOBase.truncate" title="io.IOBase.truncate"><code class="xref py py-meth docutils literal notranslate"><span class="pre">truncate()</span></code></a> will raise <a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="io.IOBase.writelines">
<span class="sig-name descname"><span class="pre">writelines</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">lines</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#io.IOBase.writelines" title="Link to this definition">¶</a></dt>
<dd><p>Write a list of lines to the stream.  Line separators are not added, so it
is usual for each of the lines provided to have a line separator at the
end.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="io.IOBase.__del__">
<span class="sig-name descname"><span class="pre">__del__</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#io.IOBase.__del__" title="Link to this definition">¶</a></dt>
<dd><p>Prepare for object destruction. <a class="reference internal" href="#io.IOBase" title="io.IOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">IOBase</span></code></a> provides a default
implementation of this method that calls the instance’s
<a class="reference internal" href="#io.IOBase.close" title="io.IOBase.close"><code class="xref py py-meth docutils literal notranslate"><span class="pre">close()</span></code></a> method.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="io.RawIOBase">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">io.</span></span><span class="sig-name descname"><span class="pre">RawIOBase</span></span><a class="headerlink" href="#io.RawIOBase" title="Link to this definition">¶</a></dt>
<dd><p>Base class for raw binary streams.  It inherits from <a class="reference internal" href="#io.IOBase" title="io.IOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">IOBase</span></code></a>.</p>
<p>Raw binary streams typically provide low-level access to an underlying OS
device or API, and do not try to encapsulate it in high-level primitives
(this functionality is done at a higher-level in buffered binary streams and text streams, described later
in this page).</p>
<p><a class="reference internal" href="#io.RawIOBase" title="io.RawIOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">RawIOBase</span></code></a> provides these methods in addition to those from
<a class="reference internal" href="#io.IOBase" title="io.IOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">IOBase</span></code></a>:</p>
<dl class="py method">
<dt class="sig sig-object py" id="io.RawIOBase.read">
<span class="sig-name descname"><span class="pre">read</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">size</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">-1</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#io.RawIOBase.read" title="Link to this definition">¶</a></dt>
<dd><p>Read up to <em>size</em> bytes from the object and return them.  As a convenience,
if <em>size</em> is unspecified or -1, all bytes until EOF are returned.
Otherwise, only one system call is ever made.  Fewer than <em>size</em> bytes may
be returned if the operating system call returns fewer than <em>size</em> bytes.</p>
<p>If 0 bytes are returned, and <em>size</em> was not 0, this indicates end of file.
If the object is in non-blocking mode and no bytes are available,
<code class="docutils literal notranslate"><span class="pre">None</span></code> is returned.</p>
<p>The default implementation defers to <a class="reference internal" href="#io.RawIOBase.readall" title="io.RawIOBase.readall"><code class="xref py py-meth docutils literal notranslate"><span class="pre">readall()</span></code></a> and
<a class="reference internal" href="#io.RawIOBase.readinto" title="io.RawIOBase.readinto"><code class="xref py py-meth docutils literal notranslate"><span class="pre">readinto()</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="io.RawIOBase.readall">
<span class="sig-name descname"><span class="pre">readall</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#io.RawIOBase.readall" title="Link to this definition">¶</a></dt>
<dd><p>Read and return all the bytes from the stream until EOF, using multiple
calls to the stream if necessary.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="io.RawIOBase.readinto">
<span class="sig-name descname"><span class="pre">readinto</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">b</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#io.RawIOBase.readinto" title="Link to this definition">¶</a></dt>
<dd><p>Read bytes into a pre-allocated, writable
<a class="reference internal" href="../glossary.html#term-bytes-like-object"><span class="xref std std-term">bytes-like object</span></a> <em>b</em>, and return the
number of bytes read.  For example, <em>b</em> might be a <a class="reference internal" href="stdtypes.html#bytearray" title="bytearray"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytearray</span></code></a>.
If the object is in non-blocking mode and no bytes
are available, <code class="docutils literal notranslate"><span class="pre">None</span></code> is returned.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="io.RawIOBase.write">
<span class="sig-name descname"><span class="pre">write</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">b</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#io.RawIOBase.write" title="Link to this definition">¶</a></dt>
<dd><p>Write the given <a class="reference internal" href="../glossary.html#term-bytes-like-object"><span class="xref std std-term">bytes-like object</span></a>, <em>b</em>, to the
underlying raw stream, and return the number of
bytes written.  This can be less than the length of <em>b</em> in
bytes, depending on specifics of the underlying raw
stream, and especially if it is in non-blocking mode.  <code class="docutils literal notranslate"><span class="pre">None</span></code> is
returned if the raw stream is set not to block and no single byte could
be readily written to it.  The caller may release or mutate <em>b</em> after
this method returns, so the implementation should only access <em>b</em>
during the method call.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="io.BufferedIOBase">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">io.</span></span><span class="sig-name descname"><span class="pre">BufferedIOBase</span></span><a class="headerlink" href="#io.BufferedIOBase" title="Link to this definition">¶</a></dt>
<dd><p>Base class for binary streams that support some kind of buffering.
It inherits from <a class="reference internal" href="#io.IOBase" title="io.IOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">IOBase</span></code></a>.</p>
<p>The main difference with <a class="reference internal" href="#io.RawIOBase" title="io.RawIOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">RawIOBase</span></code></a> is that methods <a class="reference internal" href="#io.BufferedIOBase.read" title="io.BufferedIOBase.read"><code class="xref py py-meth docutils literal notranslate"><span class="pre">read()</span></code></a>,
<a class="reference internal" href="#io.BufferedIOBase.readinto" title="io.BufferedIOBase.readinto"><code class="xref py py-meth docutils literal notranslate"><span class="pre">readinto()</span></code></a> and <a class="reference internal" href="#io.BufferedIOBase.write" title="io.BufferedIOBase.write"><code class="xref py py-meth docutils literal notranslate"><span class="pre">write()</span></code></a> will try (respectively) to read as much
input as requested or to consume all given output, at the expense of
making perhaps more than one system call.</p>
<p>In addition, those methods can raise <a class="reference internal" href="exceptions.html#BlockingIOError" title="BlockingIOError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">BlockingIOError</span></code></a> if the
underlying raw stream is in non-blocking mode and cannot take or give
enough data; unlike their <a class="reference internal" href="#io.RawIOBase" title="io.RawIOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">RawIOBase</span></code></a> counterparts, they will
never return <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
<p>Besides, the <a class="reference internal" href="#io.BufferedIOBase.read" title="io.BufferedIOBase.read"><code class="xref py py-meth docutils literal notranslate"><span class="pre">read()</span></code></a> method does not have a default
implementation that defers to <a class="reference internal" href="#io.BufferedIOBase.readinto" title="io.BufferedIOBase.readinto"><code class="xref py py-meth docutils literal notranslate"><span class="pre">readinto()</span></code></a>.</p>
<p>A typical <a class="reference internal" href="#io.BufferedIOBase" title="io.BufferedIOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">BufferedIOBase</span></code></a> implementation should not inherit from a
<a class="reference internal" href="#io.RawIOBase" title="io.RawIOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">RawIOBase</span></code></a> implementation, but wrap one, like
<a class="reference internal" href="#io.BufferedWriter" title="io.BufferedWriter"><code class="xref py py-class docutils literal notranslate"><span class="pre">BufferedWriter</span></code></a> and <a class="reference internal" href="#io.BufferedReader" title="io.BufferedReader"><code class="xref py py-class docutils literal notranslate"><span class="pre">BufferedReader</span></code></a> do.</p>
<p><a class="reference internal" href="#io.BufferedIOBase" title="io.BufferedIOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">BufferedIOBase</span></code></a> provides or overrides these data attributes and
methods in addition to those from <a class="reference internal" href="#io.IOBase" title="io.IOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">IOBase</span></code></a>:</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="io.BufferedIOBase.raw">
<span class="sig-name descname"><span class="pre">raw</span></span><a class="headerlink" href="#io.BufferedIOBase.raw" title="Link to this definition">¶</a></dt>
<dd><p>The underlying raw stream (a <a class="reference internal" href="#io.RawIOBase" title="io.RawIOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">RawIOBase</span></code></a> instance) that
<a class="reference internal" href="#io.BufferedIOBase" title="io.BufferedIOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">BufferedIOBase</span></code></a> deals with.  This is not part of the
<a class="reference internal" href="#io.BufferedIOBase" title="io.BufferedIOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">BufferedIOBase</span></code></a> API and may not exist on some implementations.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="io.BufferedIOBase.detach">
<span class="sig-name descname"><span class="pre">detach</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#io.BufferedIOBase.detach" title="Link to this definition">¶</a></dt>
<dd><p>Separate the underlying raw stream from the buffer and return it.</p>
<p>After the raw stream has been detached, the buffer is in an unusable
state.</p>
<p>Some buffers, like <a class="reference internal" href="#io.BytesIO" title="io.BytesIO"><code class="xref py py-class docutils literal notranslate"><span class="pre">BytesIO</span></code></a>, do not have the concept of a single
raw stream to return from this method.  They raise
<a class="reference internal" href="#io.UnsupportedOperation" title="io.UnsupportedOperation"><code class="xref py py-exc docutils literal notranslate"><span class="pre">UnsupportedOperation</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.1.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="io.BufferedIOBase.read">
<span class="sig-name descname"><span class="pre">read</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">size</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">-1</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#io.BufferedIOBase.read" title="Link to this definition">¶</a></dt>
<dd><p>Read and return up to <em>size</em> bytes.  If the argument is omitted, <code class="docutils literal notranslate"><span class="pre">None</span></code>,
or negative, data is read and returned until EOF is reached.  An empty
<a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a> object is returned if the stream is already at EOF.</p>
<p>If the argument is positive, and the underlying raw stream is not
interactive, multiple raw reads may be issued to satisfy the byte count
(unless EOF is reached first).  But for interactive raw streams, at most
one raw read will be issued, and a short result does not imply that EOF is
imminent.</p>
<p>A <a class="reference internal" href="exceptions.html#BlockingIOError" title="BlockingIOError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">BlockingIOError</span></code></a> is raised if the underlying raw stream is in
non blocking-mode, and has no data available at the moment.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="io.BufferedIOBase.read1">
<span class="sig-name descname"><span class="pre">read1</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">size</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">-1</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#io.BufferedIOBase.read1" title="Link to this definition">¶</a></dt>
<dd><p>Read and return up to <em>size</em> bytes, with at most one call to the
underlying raw stream’s <a class="reference internal" href="#io.RawIOBase.read" title="io.RawIOBase.read"><code class="xref py py-meth docutils literal notranslate"><span class="pre">read()</span></code></a> (or
<a class="reference internal" href="#io.RawIOBase.readinto" title="io.RawIOBase.readinto"><code class="xref py py-meth docutils literal notranslate"><span class="pre">readinto()</span></code></a>) method.  This can be useful if you are
implementing your own buffering on top of a <a class="reference internal" href="#io.BufferedIOBase" title="io.BufferedIOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">BufferedIOBase</span></code></a>
object.</p>
<p>If <em>size</em> is <code class="docutils literal notranslate"><span class="pre">-1</span></code> (the default), an arbitrary number of bytes are
returned (more than zero unless EOF is reached).</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="io.BufferedIOBase.readinto">
<span class="sig-name descname"><span class="pre">readinto</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">b</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#io.BufferedIOBase.readinto" title="Link to this definition">¶</a></dt>
<dd><p>Read bytes into a pre-allocated, writable
<a class="reference internal" href="../glossary.html#term-bytes-like-object"><span class="xref std std-term">bytes-like object</span></a> <em>b</em> and return the number of bytes read.
For example, <em>b</em> might be a <a class="reference internal" href="stdtypes.html#bytearray" title="bytearray"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytearray</span></code></a>.</p>
<p>Like <a class="reference internal" href="#io.BufferedIOBase.read" title="io.BufferedIOBase.read"><code class="xref py py-meth docutils literal notranslate"><span class="pre">read()</span></code></a>, multiple reads may be issued to the underlying raw
stream, unless the latter is interactive.</p>
<p>A <a class="reference internal" href="exceptions.html#BlockingIOError" title="BlockingIOError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">BlockingIOError</span></code></a> is raised if the underlying raw stream is in non
blocking-mode, and has no data available at the moment.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="io.BufferedIOBase.readinto1">
<span class="sig-name descname"><span class="pre">readinto1</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">b</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#io.BufferedIOBase.readinto1" title="Link to this definition">¶</a></dt>
<dd><p>Read bytes into a pre-allocated, writable
<a class="reference internal" href="../glossary.html#term-bytes-like-object"><span class="xref std std-term">bytes-like object</span></a> <em>b</em>, using at most one call to
the underlying raw stream’s <a class="reference internal" href="#io.RawIOBase.read" title="io.RawIOBase.read"><code class="xref py py-meth docutils literal notranslate"><span class="pre">read()</span></code></a> (or
<a class="reference internal" href="#io.RawIOBase.readinto" title="io.RawIOBase.readinto"><code class="xref py py-meth docutils literal notranslate"><span class="pre">readinto()</span></code></a>) method. Return the number of bytes read.</p>
<p>A <a class="reference internal" href="exceptions.html#BlockingIOError" title="BlockingIOError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">BlockingIOError</span></code></a> is raised if the underlying raw stream is in non
blocking-mode, and has no data available at the moment.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="io.BufferedIOBase.write">
<span class="sig-name descname"><span class="pre">write</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">b</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#io.BufferedIOBase.write" title="Link to this definition">¶</a></dt>
<dd><p>Write the given <a class="reference internal" href="../glossary.html#term-bytes-like-object"><span class="xref std std-term">bytes-like object</span></a>, <em>b</em>, and return the number
of bytes written (always equal to the length of <em>b</em> in bytes, since if
the write fails an <a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a> will be raised).  Depending on the
actual implementation, these bytes may be readily written to the
underlying stream, or held in a buffer for performance and latency
reasons.</p>
<p>When in non-blocking mode, a <a class="reference internal" href="exceptions.html#BlockingIOError" title="BlockingIOError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">BlockingIOError</span></code></a> is raised if the
data needed to be written to the raw stream but it couldn’t accept
all the data without blocking.</p>
<p>The caller may release or mutate <em>b</em> after this method returns,
so the implementation should only access <em>b</em> during the method call.</p>
</dd></dl>

</dd></dl>

</section>
<section id="raw-file-i-o">
<h3>Raw File I/O<a class="headerlink" href="#raw-file-i-o" title="Link to this heading">¶</a></h3>
<dl class="py class">
<dt class="sig sig-object py" id="io.FileIO">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">io.</span></span><span class="sig-name descname"><span class="pre">FileIO</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">mode</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'r'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">closefd</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">opener</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#io.FileIO" title="Link to this definition">¶</a></dt>
<dd><p>A raw binary stream representing an OS-level file containing bytes data.  It
inherits from <a class="reference internal" href="#io.RawIOBase" title="io.RawIOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">RawIOBase</span></code></a>.</p>
<p>The <em>name</em> can be one of two things:</p>
<ul class="simple">
<li><p>a character string or <a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a> object representing the path to the
file which will be opened. In this case closefd must be <code class="docutils literal notranslate"><span class="pre">True</span></code> (the default)
otherwise an error will be raised.</p></li>
<li><p>an integer representing the number of an existing OS-level file descriptor
to which the resulting <a class="reference internal" href="#io.FileIO" title="io.FileIO"><code class="xref py py-class docutils literal notranslate"><span class="pre">FileIO</span></code></a> object will give access. When the
FileIO object is closed this fd will be closed as well, unless <em>closefd</em>
is set to <code class="docutils literal notranslate"><span class="pre">False</span></code>.</p></li>
</ul>
<p>The <em>mode</em> can be <code class="docutils literal notranslate"><span class="pre">'r'</span></code>, <code class="docutils literal notranslate"><span class="pre">'w'</span></code>, <code class="docutils literal notranslate"><span class="pre">'x'</span></code> or <code class="docutils literal notranslate"><span class="pre">'a'</span></code> for reading
(default), writing, exclusive creation or appending. The file will be
created if it doesn’t exist when opened for writing or appending; it will be
truncated when opened for writing. <a class="reference internal" href="exceptions.html#FileExistsError" title="FileExistsError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">FileExistsError</span></code></a> will be raised if
it already exists when opened for creating. Opening a file for creating
implies writing, so this mode behaves in a similar way to <code class="docutils literal notranslate"><span class="pre">'w'</span></code>. Add a
<code class="docutils literal notranslate"><span class="pre">'+'</span></code> to the mode to allow simultaneous reading and writing.</p>
<p>The <a class="reference internal" href="#io.RawIOBase.read" title="io.RawIOBase.read"><code class="xref py py-meth docutils literal notranslate"><span class="pre">read()</span></code></a> (when called with a positive argument),
<a class="reference internal" href="#io.RawIOBase.readinto" title="io.RawIOBase.readinto"><code class="xref py py-meth docutils literal notranslate"><span class="pre">readinto()</span></code></a> and <a class="reference internal" href="#io.RawIOBase.write" title="io.RawIOBase.write"><code class="xref py py-meth docutils literal notranslate"><span class="pre">write()</span></code></a> methods on this
class will only make one system call.</p>
<p>A custom opener can be used by passing a callable as <em>opener</em>. The underlying
file descriptor for the file object is then obtained by calling <em>opener</em> with
(<em>name</em>, <em>flags</em>). <em>opener</em> must return an open file descriptor (passing
<a class="reference internal" href="os.html#os.open" title="os.open"><code class="xref py py-mod docutils literal notranslate"><span class="pre">os.open</span></code></a> as <em>opener</em> results in functionality similar to passing
<code class="docutils literal notranslate"><span class="pre">None</span></code>).</p>
<p>The newly created file is <a class="reference internal" href="os.html#fd-inheritance"><span class="std std-ref">non-inheritable</span></a>.</p>
<p>See the <a class="reference internal" href="functions.html#open" title="open"><code class="xref py py-func docutils literal notranslate"><span class="pre">open()</span></code></a> built-in function for examples on using the <em>opener</em>
parameter.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>The <em>opener</em> parameter was added.
The <code class="docutils literal notranslate"><span class="pre">'x'</span></code> mode was added.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>The file is now non-inheritable.</p>
</div>
<p><a class="reference internal" href="#io.FileIO" title="io.FileIO"><code class="xref py py-class docutils literal notranslate"><span class="pre">FileIO</span></code></a> provides these data attributes in addition to those from
<a class="reference internal" href="#io.RawIOBase" title="io.RawIOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">RawIOBase</span></code></a> and <a class="reference internal" href="#io.IOBase" title="io.IOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">IOBase</span></code></a>:</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="io.FileIO.mode">
<span class="sig-name descname"><span class="pre">mode</span></span><a class="headerlink" href="#io.FileIO.mode" title="Link to this definition">¶</a></dt>
<dd><p>The mode as given in the constructor.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="io.FileIO.name">
<span class="sig-name descname"><span class="pre">name</span></span><a class="headerlink" href="#io.FileIO.name" title="Link to this definition">¶</a></dt>
<dd><p>The file name.  This is the file descriptor of the file when no name is
given in the constructor.</p>
</dd></dl>

</dd></dl>

</section>
<section id="buffered-streams">
<h3>Buffered Streams<a class="headerlink" href="#buffered-streams" title="Link to this heading">¶</a></h3>
<p>Buffered I/O streams provide a higher-level interface to an I/O device
than raw I/O does.</p>
<dl class="py class">
<dt class="sig sig-object py" id="io.BytesIO">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">io.</span></span><span class="sig-name descname"><span class="pre">BytesIO</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">initial_bytes</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">b''</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#io.BytesIO" title="Link to this definition">¶</a></dt>
<dd><p>A binary stream using an in-memory bytes buffer.  It inherits from
<a class="reference internal" href="#io.BufferedIOBase" title="io.BufferedIOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">BufferedIOBase</span></code></a>.  The buffer is discarded when the
<a class="reference internal" href="#io.IOBase.close" title="io.IOBase.close"><code class="xref py py-meth docutils literal notranslate"><span class="pre">close()</span></code></a> method is called.</p>
<p>The optional argument <em>initial_bytes</em> is a <a class="reference internal" href="../glossary.html#term-bytes-like-object"><span class="xref std std-term">bytes-like object</span></a> that
contains initial data.</p>
<p><a class="reference internal" href="#io.BytesIO" title="io.BytesIO"><code class="xref py py-class docutils literal notranslate"><span class="pre">BytesIO</span></code></a> provides or overrides these methods in addition to those
from <a class="reference internal" href="#io.BufferedIOBase" title="io.BufferedIOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">BufferedIOBase</span></code></a> and <a class="reference internal" href="#io.IOBase" title="io.IOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">IOBase</span></code></a>:</p>
<dl class="py method">
<dt class="sig sig-object py" id="io.BytesIO.getbuffer">
<span class="sig-name descname"><span class="pre">getbuffer</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#io.BytesIO.getbuffer" title="Link to this definition">¶</a></dt>
<dd><p>Return a readable and writable view over the contents of the buffer
without copying them.  Also, mutating the view will transparently
update the contents of the buffer:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">b</span> <span class="o">=</span> <span class="n">io</span><span class="o">.</span><span class="n">BytesIO</span><span class="p">(</span><span class="sa">b</span><span class="s2">&quot;abcdef&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">view</span> <span class="o">=</span> <span class="n">b</span><span class="o">.</span><span class="n">getbuffer</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">view</span><span class="p">[</span><span class="mi">2</span><span class="p">:</span><span class="mi">4</span><span class="p">]</span> <span class="o">=</span> <span class="sa">b</span><span class="s2">&quot;56&quot;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">b</span><span class="o">.</span><span class="n">getvalue</span><span class="p">()</span>
<span class="go">b&#39;ab56ef&#39;</span>
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>As long as the view exists, the <a class="reference internal" href="#io.BytesIO" title="io.BytesIO"><code class="xref py py-class docutils literal notranslate"><span class="pre">BytesIO</span></code></a> object cannot be
resized or closed.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="io.BytesIO.getvalue">
<span class="sig-name descname"><span class="pre">getvalue</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#io.BytesIO.getvalue" title="Link to this definition">¶</a></dt>
<dd><p>Return <a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a> containing the entire contents of the buffer.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="io.BytesIO.read1">
<span class="sig-name descname"><span class="pre">read1</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">size</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">-1</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#io.BytesIO.read1" title="Link to this definition">¶</a></dt>
<dd><p>In <a class="reference internal" href="#io.BytesIO" title="io.BytesIO"><code class="xref py py-class docutils literal notranslate"><span class="pre">BytesIO</span></code></a>, this is the same as <a class="reference internal" href="#io.BufferedIOBase.read" title="io.BufferedIOBase.read"><code class="xref py py-meth docutils literal notranslate"><span class="pre">read()</span></code></a>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span>The <em>size</em> argument is now optional.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="io.BytesIO.readinto1">
<span class="sig-name descname"><span class="pre">readinto1</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">b</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#io.BytesIO.readinto1" title="Link to this definition">¶</a></dt>
<dd><p>In <a class="reference internal" href="#io.BytesIO" title="io.BytesIO"><code class="xref py py-class docutils literal notranslate"><span class="pre">BytesIO</span></code></a>, this is the same as <a class="reference internal" href="#io.BufferedIOBase.readinto" title="io.BufferedIOBase.readinto"><code class="xref py py-meth docutils literal notranslate"><span class="pre">readinto()</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.</span></p>
</div>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="io.BufferedReader">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">io.</span></span><span class="sig-name descname"><span class="pre">BufferedReader</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">raw</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">buffer_size</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">DEFAULT_BUFFER_SIZE</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#io.BufferedReader" title="Link to this definition">¶</a></dt>
<dd><p>A buffered binary stream providing higher-level access to a readable, non
seekable <a class="reference internal" href="#io.RawIOBase" title="io.RawIOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">RawIOBase</span></code></a> raw binary stream.  It inherits from
<a class="reference internal" href="#io.BufferedIOBase" title="io.BufferedIOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">BufferedIOBase</span></code></a>.</p>
<p>When reading data from this object, a larger amount of data may be
requested from the underlying raw stream, and kept in an internal buffer.
The buffered data can then be returned directly on subsequent reads.</p>
<p>The constructor creates a <a class="reference internal" href="#io.BufferedReader" title="io.BufferedReader"><code class="xref py py-class docutils literal notranslate"><span class="pre">BufferedReader</span></code></a> for the given readable
<em>raw</em> stream and <em>buffer_size</em>.  If <em>buffer_size</em> is omitted,
<a class="reference internal" href="#io.DEFAULT_BUFFER_SIZE" title="io.DEFAULT_BUFFER_SIZE"><code class="xref py py-data docutils literal notranslate"><span class="pre">DEFAULT_BUFFER_SIZE</span></code></a> is used.</p>
<p><a class="reference internal" href="#io.BufferedReader" title="io.BufferedReader"><code class="xref py py-class docutils literal notranslate"><span class="pre">BufferedReader</span></code></a> provides or overrides these methods in addition to
those from <a class="reference internal" href="#io.BufferedIOBase" title="io.BufferedIOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">BufferedIOBase</span></code></a> and <a class="reference internal" href="#io.IOBase" title="io.IOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">IOBase</span></code></a>:</p>
<dl class="py method">
<dt class="sig sig-object py" id="io.BufferedReader.peek">
<span class="sig-name descname"><span class="pre">peek</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">size</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#io.BufferedReader.peek" title="Link to this definition">¶</a></dt>
<dd><p>Return bytes from the stream without advancing the position.  At most one
single read on the raw stream is done to satisfy the call. The number of
bytes returned may be less or more than requested.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="io.BufferedReader.read">
<span class="sig-name descname"><span class="pre">read</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">size</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">-1</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#io.BufferedReader.read" title="Link to this definition">¶</a></dt>
<dd><p>Read and return <em>size</em> bytes, or if <em>size</em> is not given or negative, until
EOF or if the read call would block in non-blocking mode.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="io.BufferedReader.read1">
<span class="sig-name descname"><span class="pre">read1</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">size</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">-1</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#io.BufferedReader.read1" title="Link to this definition">¶</a></dt>
<dd><p>Read and return up to <em>size</em> bytes with only one call on the raw stream.
If at least one byte is buffered, only buffered bytes are returned.
Otherwise, one raw stream read call is made.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span>The <em>size</em> argument is now optional.</p>
</div>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="io.BufferedWriter">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">io.</span></span><span class="sig-name descname"><span class="pre">BufferedWriter</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">raw</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">buffer_size</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">DEFAULT_BUFFER_SIZE</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#io.BufferedWriter" title="Link to this definition">¶</a></dt>
<dd><p>A buffered binary stream providing higher-level access to a writeable, non
seekable <a class="reference internal" href="#io.RawIOBase" title="io.RawIOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">RawIOBase</span></code></a> raw binary stream.  It inherits from
<a class="reference internal" href="#io.BufferedIOBase" title="io.BufferedIOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">BufferedIOBase</span></code></a>.</p>
<p>When writing to this object, data is normally placed into an internal
buffer.  The buffer will be written out to the underlying <a class="reference internal" href="#io.RawIOBase" title="io.RawIOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">RawIOBase</span></code></a>
object under various conditions, including:</p>
<ul class="simple">
<li><p>when the buffer gets too small for all pending data;</p></li>
<li><p>when <a class="reference internal" href="#io.BufferedWriter.flush" title="io.BufferedWriter.flush"><code class="xref py py-meth docutils literal notranslate"><span class="pre">flush()</span></code></a> is called;</p></li>
<li><p>when a <a class="reference internal" href="#io.IOBase.seek" title="io.IOBase.seek"><code class="xref py py-meth docutils literal notranslate"><span class="pre">seek()</span></code></a> is requested (for <a class="reference internal" href="#io.BufferedRandom" title="io.BufferedRandom"><code class="xref py py-class docutils literal notranslate"><span class="pre">BufferedRandom</span></code></a> objects);</p></li>
<li><p>when the <a class="reference internal" href="#io.BufferedWriter" title="io.BufferedWriter"><code class="xref py py-class docutils literal notranslate"><span class="pre">BufferedWriter</span></code></a> object is closed or destroyed.</p></li>
</ul>
<p>The constructor creates a <a class="reference internal" href="#io.BufferedWriter" title="io.BufferedWriter"><code class="xref py py-class docutils literal notranslate"><span class="pre">BufferedWriter</span></code></a> for the given writeable
<em>raw</em> stream.  If the <em>buffer_size</em> is not given, it defaults to
<a class="reference internal" href="#io.DEFAULT_BUFFER_SIZE" title="io.DEFAULT_BUFFER_SIZE"><code class="xref py py-data docutils literal notranslate"><span class="pre">DEFAULT_BUFFER_SIZE</span></code></a>.</p>
<p><a class="reference internal" href="#io.BufferedWriter" title="io.BufferedWriter"><code class="xref py py-class docutils literal notranslate"><span class="pre">BufferedWriter</span></code></a> provides or overrides these methods in addition to
those from <a class="reference internal" href="#io.BufferedIOBase" title="io.BufferedIOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">BufferedIOBase</span></code></a> and <a class="reference internal" href="#io.IOBase" title="io.IOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">IOBase</span></code></a>:</p>
<dl class="py method">
<dt class="sig sig-object py" id="io.BufferedWriter.flush">
<span class="sig-name descname"><span class="pre">flush</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#io.BufferedWriter.flush" title="Link to this definition">¶</a></dt>
<dd><p>Force bytes held in the buffer into the raw stream.  A
<a class="reference internal" href="exceptions.html#BlockingIOError" title="BlockingIOError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">BlockingIOError</span></code></a> should be raised if the raw stream blocks.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="io.BufferedWriter.write">
<span class="sig-name descname"><span class="pre">write</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">b</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#io.BufferedWriter.write" title="Link to this definition">¶</a></dt>
<dd><p>Write the <a class="reference internal" href="../glossary.html#term-bytes-like-object"><span class="xref std std-term">bytes-like object</span></a>, <em>b</em>, and return the
number of bytes written.  When in non-blocking mode, a
<a class="reference internal" href="exceptions.html#BlockingIOError" title="BlockingIOError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">BlockingIOError</span></code></a> is raised if the buffer needs to be written out but
the raw stream blocks.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="io.BufferedRandom">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">io.</span></span><span class="sig-name descname"><span class="pre">BufferedRandom</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">raw</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">buffer_size</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">DEFAULT_BUFFER_SIZE</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#io.BufferedRandom" title="Link to this definition">¶</a></dt>
<dd><p>A buffered binary stream providing higher-level access to a seekable
<a class="reference internal" href="#io.RawIOBase" title="io.RawIOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">RawIOBase</span></code></a> raw binary stream.  It inherits from <a class="reference internal" href="#io.BufferedReader" title="io.BufferedReader"><code class="xref py py-class docutils literal notranslate"><span class="pre">BufferedReader</span></code></a>
and <a class="reference internal" href="#io.BufferedWriter" title="io.BufferedWriter"><code class="xref py py-class docutils literal notranslate"><span class="pre">BufferedWriter</span></code></a>.</p>
<p>The constructor creates a reader and writer for a seekable raw stream, given
in the first argument.  If the <em>buffer_size</em> is omitted it defaults to
<a class="reference internal" href="#io.DEFAULT_BUFFER_SIZE" title="io.DEFAULT_BUFFER_SIZE"><code class="xref py py-data docutils literal notranslate"><span class="pre">DEFAULT_BUFFER_SIZE</span></code></a>.</p>
<p><a class="reference internal" href="#io.BufferedRandom" title="io.BufferedRandom"><code class="xref py py-class docutils literal notranslate"><span class="pre">BufferedRandom</span></code></a> is capable of anything <a class="reference internal" href="#io.BufferedReader" title="io.BufferedReader"><code class="xref py py-class docutils literal notranslate"><span class="pre">BufferedReader</span></code></a> or
<a class="reference internal" href="#io.BufferedWriter" title="io.BufferedWriter"><code class="xref py py-class docutils literal notranslate"><span class="pre">BufferedWriter</span></code></a> can do.  In addition, <a class="reference internal" href="#io.IOBase.seek" title="io.IOBase.seek"><code class="xref py py-meth docutils literal notranslate"><span class="pre">seek()</span></code></a> and
<a class="reference internal" href="#io.IOBase.tell" title="io.IOBase.tell"><code class="xref py py-meth docutils literal notranslate"><span class="pre">tell()</span></code></a> are guaranteed to be implemented.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="io.BufferedRWPair">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">io.</span></span><span class="sig-name descname"><span class="pre">BufferedRWPair</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">reader</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">writer</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">buffer_size</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">DEFAULT_BUFFER_SIZE</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#io.BufferedRWPair" title="Link to this definition">¶</a></dt>
<dd><p>A buffered binary stream providing higher-level access to two non seekable
<a class="reference internal" href="#io.RawIOBase" title="io.RawIOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">RawIOBase</span></code></a> raw binary streams—one readable, the other writeable.
It inherits from <a class="reference internal" href="#io.BufferedIOBase" title="io.BufferedIOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">BufferedIOBase</span></code></a>.</p>
<p><em>reader</em> and <em>writer</em> are <a class="reference internal" href="#io.RawIOBase" title="io.RawIOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">RawIOBase</span></code></a> objects that are readable and
writeable respectively.  If the <em>buffer_size</em> is omitted it defaults to
<a class="reference internal" href="#io.DEFAULT_BUFFER_SIZE" title="io.DEFAULT_BUFFER_SIZE"><code class="xref py py-data docutils literal notranslate"><span class="pre">DEFAULT_BUFFER_SIZE</span></code></a>.</p>
<p><a class="reference internal" href="#io.BufferedRWPair" title="io.BufferedRWPair"><code class="xref py py-class docutils literal notranslate"><span class="pre">BufferedRWPair</span></code></a> implements all of <a class="reference internal" href="#io.BufferedIOBase" title="io.BufferedIOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">BufferedIOBase</span></code></a>'s methods
except for <a class="reference internal" href="#io.BufferedIOBase.detach" title="io.BufferedIOBase.detach"><code class="xref py py-meth docutils literal notranslate"><span class="pre">detach()</span></code></a>, which raises
<a class="reference internal" href="#io.UnsupportedOperation" title="io.UnsupportedOperation"><code class="xref py py-exc docutils literal notranslate"><span class="pre">UnsupportedOperation</span></code></a>.</p>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p><a class="reference internal" href="#io.BufferedRWPair" title="io.BufferedRWPair"><code class="xref py py-class docutils literal notranslate"><span class="pre">BufferedRWPair</span></code></a> does not attempt to synchronize accesses to
its underlying raw streams.  You should not pass it the same object
as reader and writer; use <a class="reference internal" href="#io.BufferedRandom" title="io.BufferedRandom"><code class="xref py py-class docutils literal notranslate"><span class="pre">BufferedRandom</span></code></a> instead.</p>
</div>
</dd></dl>

</section>
<section id="id1">
<h3>Text I/O<a class="headerlink" href="#id1" title="Link to this heading">¶</a></h3>
<dl class="py class">
<dt class="sig sig-object py" id="io.TextIOBase">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">io.</span></span><span class="sig-name descname"><span class="pre">TextIOBase</span></span><a class="headerlink" href="#io.TextIOBase" title="Link to this definition">¶</a></dt>
<dd><p>Base class for text streams.  This class provides a character and line based
interface to stream I/O.  It inherits from <a class="reference internal" href="#io.IOBase" title="io.IOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">IOBase</span></code></a>.</p>
<p><a class="reference internal" href="#io.TextIOBase" title="io.TextIOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">TextIOBase</span></code></a> provides or overrides these data attributes and
methods in addition to those from <a class="reference internal" href="#io.IOBase" title="io.IOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">IOBase</span></code></a>:</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="io.TextIOBase.encoding">
<span class="sig-name descname"><span class="pre">encoding</span></span><a class="headerlink" href="#io.TextIOBase.encoding" title="Link to this definition">¶</a></dt>
<dd><p>The name of the encoding used to decode the stream’s bytes into
strings, and to encode strings into bytes.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="io.TextIOBase.errors">
<span class="sig-name descname"><span class="pre">errors</span></span><a class="headerlink" href="#io.TextIOBase.errors" title="Link to this definition">¶</a></dt>
<dd><p>The error setting of the decoder or encoder.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="io.TextIOBase.newlines">
<span class="sig-name descname"><span class="pre">newlines</span></span><a class="headerlink" href="#io.TextIOBase.newlines" title="Link to this definition">¶</a></dt>
<dd><p>A string, a tuple of strings, or <code class="docutils literal notranslate"><span class="pre">None</span></code>, indicating the newlines
translated so far.  Depending on the implementation and the initial
constructor flags, this may not be available.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="io.TextIOBase.buffer">
<span class="sig-name descname"><span class="pre">buffer</span></span><a class="headerlink" href="#io.TextIOBase.buffer" title="Link to this definition">¶</a></dt>
<dd><p>The underlying binary buffer (a <a class="reference internal" href="#io.BufferedIOBase" title="io.BufferedIOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">BufferedIOBase</span></code></a> instance) that
<a class="reference internal" href="#io.TextIOBase" title="io.TextIOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">TextIOBase</span></code></a> deals with.  This is not part of the
<a class="reference internal" href="#io.TextIOBase" title="io.TextIOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">TextIOBase</span></code></a> API and may not exist in some implementations.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="io.TextIOBase.detach">
<span class="sig-name descname"><span class="pre">detach</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#io.TextIOBase.detach" title="Link to this definition">¶</a></dt>
<dd><p>Separate the underlying binary buffer from the <a class="reference internal" href="#io.TextIOBase" title="io.TextIOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">TextIOBase</span></code></a> and
return it.</p>
<p>After the underlying buffer has been detached, the <a class="reference internal" href="#io.TextIOBase" title="io.TextIOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">TextIOBase</span></code></a> is
in an unusable state.</p>
<p>Some <a class="reference internal" href="#io.TextIOBase" title="io.TextIOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">TextIOBase</span></code></a> implementations, like <a class="reference internal" href="#io.StringIO" title="io.StringIO"><code class="xref py py-class docutils literal notranslate"><span class="pre">StringIO</span></code></a>, may not
have the concept of an underlying buffer and calling this method will
raise <a class="reference internal" href="#io.UnsupportedOperation" title="io.UnsupportedOperation"><code class="xref py py-exc docutils literal notranslate"><span class="pre">UnsupportedOperation</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.1.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="io.TextIOBase.read">
<span class="sig-name descname"><span class="pre">read</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">size</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">-1</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#io.TextIOBase.read" title="Link to this definition">¶</a></dt>
<dd><p>Read and return at most <em>size</em> characters from the stream as a single
<a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>.  If <em>size</em> is negative or <code class="docutils literal notranslate"><span class="pre">None</span></code>, reads until EOF.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="io.TextIOBase.readline">
<span class="sig-name descname"><span class="pre">readline</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">size</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">-1</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#io.TextIOBase.readline" title="Link to this definition">¶</a></dt>
<dd><p>Read until newline or EOF and return a single <a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>.  If the stream is
already at EOF, an empty string is returned.</p>
<p>If <em>size</em> is specified, at most <em>size</em> characters will be read.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="io.TextIOBase.seek">
<span class="sig-name descname"><span class="pre">seek</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">offset</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">whence</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">SEEK_SET</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#io.TextIOBase.seek" title="Link to this definition">¶</a></dt>
<dd><p>Change the stream position to the given <em>offset</em>.  Behaviour depends on
the <em>whence</em> parameter.  The default value for <em>whence</em> is
<code class="xref py py-data docutils literal notranslate"><span class="pre">SEEK_SET</span></code>.</p>
<ul class="simple">
<li><p><code class="xref py py-data docutils literal notranslate"><span class="pre">SEEK_SET</span></code> or <code class="docutils literal notranslate"><span class="pre">0</span></code>: seek from the start of the stream
(the default); <em>offset</em> must either be a number returned by
<a class="reference internal" href="#io.TextIOBase.tell" title="io.TextIOBase.tell"><code class="xref py py-meth docutils literal notranslate"><span class="pre">TextIOBase.tell()</span></code></a>, or zero.  Any other <em>offset</em> value
produces undefined behaviour.</p></li>
<li><p><code class="xref py py-data docutils literal notranslate"><span class="pre">SEEK_CUR</span></code> or <code class="docutils literal notranslate"><span class="pre">1</span></code>: “seek” to the current position;
<em>offset</em> must be zero, which is a no-operation (all other values
are unsupported).</p></li>
<li><p><code class="xref py py-data docutils literal notranslate"><span class="pre">SEEK_END</span></code> or <code class="docutils literal notranslate"><span class="pre">2</span></code>: seek to the end of the stream;
<em>offset</em> must be zero (all other values are unsupported).</p></li>
</ul>
<p>Return the new absolute position as an opaque number.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.1: </span>The <code class="xref py py-data docutils literal notranslate"><span class="pre">SEEK_*</span></code> constants.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="io.TextIOBase.tell">
<span class="sig-name descname"><span class="pre">tell</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#io.TextIOBase.tell" title="Link to this definition">¶</a></dt>
<dd><p>Return the current stream position as an opaque number.  The number
does not usually represent a number of bytes in the underlying
binary storage.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="io.TextIOBase.write">
<span class="sig-name descname"><span class="pre">write</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">s</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#io.TextIOBase.write" title="Link to this definition">¶</a></dt>
<dd><p>Write the string <em>s</em> to the stream and return the number of characters
written.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="io.TextIOWrapper">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">io.</span></span><span class="sig-name descname"><span class="pre">TextIOWrapper</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">buffer</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">encoding</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">errors</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">newline</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">line_buffering</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">write_through</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#io.TextIOWrapper" title="Link to this definition">¶</a></dt>
<dd><p>A buffered text stream providing higher-level access to a
<a class="reference internal" href="#io.BufferedIOBase" title="io.BufferedIOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">BufferedIOBase</span></code></a> buffered binary stream.  It inherits from
<a class="reference internal" href="#io.TextIOBase" title="io.TextIOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">TextIOBase</span></code></a>.</p>
<p><em>encoding</em> gives the name of the encoding that the stream will be decoded or
encoded with.  It defaults to <a class="reference internal" href="locale.html#locale.getencoding" title="locale.getencoding"><code class="xref py py-func docutils literal notranslate"><span class="pre">locale.getencoding()</span></code></a>.
<code class="docutils literal notranslate"><span class="pre">encoding=&quot;locale&quot;</span></code> can be used to specify the current locale’s encoding
explicitly. See <a class="reference internal" href="#io-text-encoding"><span class="std std-ref">Text Encoding</span></a> for more information.</p>
<p><em>errors</em> is an optional string that specifies how encoding and decoding
errors are to be handled.  Pass <code class="docutils literal notranslate"><span class="pre">'strict'</span></code> to raise a <a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a>
exception if there is an encoding error (the default of <code class="docutils literal notranslate"><span class="pre">None</span></code> has the same
effect), or pass <code class="docutils literal notranslate"><span class="pre">'ignore'</span></code> to ignore errors.  (Note that ignoring encoding
errors can lead to data loss.)  <code class="docutils literal notranslate"><span class="pre">'replace'</span></code> causes a replacement marker
(such as <code class="docutils literal notranslate"><span class="pre">'?'</span></code>) to be inserted where there is malformed data.
<code class="docutils literal notranslate"><span class="pre">'backslashreplace'</span></code> causes malformed data to be replaced by a
backslashed escape sequence.  When writing, <code class="docutils literal notranslate"><span class="pre">'xmlcharrefreplace'</span></code>
(replace with the appropriate XML character reference)  or <code class="docutils literal notranslate"><span class="pre">'namereplace'</span></code>
(replace with <code class="docutils literal notranslate"><span class="pre">\N{...}</span></code> escape sequences) can be used.  Any other error
handling name that has been registered with
<a class="reference internal" href="codecs.html#codecs.register_error" title="codecs.register_error"><code class="xref py py-func docutils literal notranslate"><span class="pre">codecs.register_error()</span></code></a> is also valid.</p>
<p id="index-4"><em>newline</em> controls how line endings are handled.  It can be <code class="docutils literal notranslate"><span class="pre">None</span></code>,
<code class="docutils literal notranslate"><span class="pre">''</span></code>, <code class="docutils literal notranslate"><span class="pre">'\n'</span></code>, <code class="docutils literal notranslate"><span class="pre">'\r'</span></code>, and <code class="docutils literal notranslate"><span class="pre">'\r\n'</span></code>.  It works as follows:</p>
<ul class="simple">
<li><p>When reading input from the stream, if <em>newline</em> is <code class="docutils literal notranslate"><span class="pre">None</span></code>,
<a class="reference internal" href="../glossary.html#term-universal-newlines"><span class="xref std std-term">universal newlines</span></a> mode is enabled.  Lines in the input can end in
<code class="docutils literal notranslate"><span class="pre">'\n'</span></code>, <code class="docutils literal notranslate"><span class="pre">'\r'</span></code>, or <code class="docutils literal notranslate"><span class="pre">'\r\n'</span></code>, and these are translated into <code class="docutils literal notranslate"><span class="pre">'\n'</span></code>
before being returned to the caller.  If <em>newline</em> is <code class="docutils literal notranslate"><span class="pre">''</span></code>, universal
newlines mode is enabled, but line endings are returned to the caller
untranslated.  If <em>newline</em> has any of the other legal values, input lines
are only terminated by the given string, and the line ending is returned to
the caller untranslated.</p></li>
<li><p>When writing output to the stream, if <em>newline</em> is <code class="docutils literal notranslate"><span class="pre">None</span></code>, any <code class="docutils literal notranslate"><span class="pre">'\n'</span></code>
characters written are translated to the system default line separator,
<a class="reference internal" href="os.html#os.linesep" title="os.linesep"><code class="xref py py-data docutils literal notranslate"><span class="pre">os.linesep</span></code></a>.  If <em>newline</em> is <code class="docutils literal notranslate"><span class="pre">''</span></code> or <code class="docutils literal notranslate"><span class="pre">'\n'</span></code>, no translation
takes place.  If <em>newline</em> is any of the other legal values, any <code class="docutils literal notranslate"><span class="pre">'\n'</span></code>
characters written are translated to the given string.</p></li>
</ul>
<p>If <em>line_buffering</em> is <code class="docutils literal notranslate"><span class="pre">True</span></code>, <a class="reference internal" href="#io.IOBase.flush" title="io.IOBase.flush"><code class="xref py py-meth docutils literal notranslate"><span class="pre">flush()</span></code></a> is implied when a call to
write contains a newline character or a carriage return.</p>
<p>If <em>write_through</em> is <code class="docutils literal notranslate"><span class="pre">True</span></code>, calls to <a class="reference internal" href="#io.BufferedIOBase.write" title="io.BufferedIOBase.write"><code class="xref py py-meth docutils literal notranslate"><span class="pre">write()</span></code></a> are guaranteed
not to be buffered: any data written on the <a class="reference internal" href="#io.TextIOWrapper" title="io.TextIOWrapper"><code class="xref py py-class docutils literal notranslate"><span class="pre">TextIOWrapper</span></code></a>
object is immediately handled to its underlying binary <em>buffer</em>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>The <em>write_through</em> argument has been added.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>The default <em>encoding</em> is now <code class="docutils literal notranslate"><span class="pre">locale.getpreferredencoding(False)</span></code>
instead of <code class="docutils literal notranslate"><span class="pre">locale.getpreferredencoding()</span></code>. Don’t change temporary the
locale encoding using <a class="reference internal" href="locale.html#locale.setlocale" title="locale.setlocale"><code class="xref py py-func docutils literal notranslate"><span class="pre">locale.setlocale()</span></code></a>, use the current locale
encoding instead of the user preferred encoding.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.10: </span>The <em>encoding</em> argument now supports the <code class="docutils literal notranslate"><span class="pre">&quot;locale&quot;</span></code> dummy encoding name.</p>
</div>
<p><a class="reference internal" href="#io.TextIOWrapper" title="io.TextIOWrapper"><code class="xref py py-class docutils literal notranslate"><span class="pre">TextIOWrapper</span></code></a> provides these data attributes and methods in
addition to those from <a class="reference internal" href="#io.TextIOBase" title="io.TextIOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">TextIOBase</span></code></a> and <a class="reference internal" href="#io.IOBase" title="io.IOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">IOBase</span></code></a>:</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="io.TextIOWrapper.line_buffering">
<span class="sig-name descname"><span class="pre">line_buffering</span></span><a class="headerlink" href="#io.TextIOWrapper.line_buffering" title="Link to this definition">¶</a></dt>
<dd><p>Whether line buffering is enabled.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="io.TextIOWrapper.write_through">
<span class="sig-name descname"><span class="pre">write_through</span></span><a class="headerlink" href="#io.TextIOWrapper.write_through" title="Link to this definition">¶</a></dt>
<dd><p>Whether writes are passed immediately to the underlying binary
buffer.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="io.TextIOWrapper.reconfigure">
<span class="sig-name descname"><span class="pre">reconfigure</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">encoding</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">errors</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">newline</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">line_buffering</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">write_through</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#io.TextIOWrapper.reconfigure" title="Link to this definition">¶</a></dt>
<dd><p>Reconfigure this text stream using new settings for <em>encoding</em>,
<em>errors</em>, <em>newline</em>, <em>line_buffering</em> and <em>write_through</em>.</p>
<p>Parameters not specified keep current settings, except
<code class="docutils literal notranslate"><span class="pre">errors='strict'</span></code> is used when <em>encoding</em> is specified but
<em>errors</em> is not specified.</p>
<p>It is not possible to change the encoding or newline if some data
has already been read from the stream. On the other hand, changing
encoding after write is possible.</p>
<p>This method does an implicit stream flush before setting the
new parameters.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.11: </span>The method supports <code class="docutils literal notranslate"><span class="pre">encoding=&quot;locale&quot;</span></code> option.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="io.TextIOWrapper.seek">
<span class="sig-name descname"><span class="pre">seek</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">cookie</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">whence</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">os.SEEK_SET</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#io.TextIOWrapper.seek" title="Link to this definition">¶</a></dt>
<dd><p>Set the stream position.
Return the new stream position as an <a class="reference internal" href="functions.html#int" title="int"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a>.</p>
<p>Four operations are supported,
given by the following argument combinations:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">seek(0,</span> <span class="pre">SEEK_SET)</span></code>: Rewind to the start of the stream.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">seek(cookie,</span> <span class="pre">SEEK_SET)</span></code>: Restore a previous position;
<em>cookie</em> <strong>must be</strong> a number returned by <a class="reference internal" href="#io.TextIOWrapper.tell" title="io.TextIOWrapper.tell"><code class="xref py py-meth docutils literal notranslate"><span class="pre">tell()</span></code></a>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">seek(0,</span> <span class="pre">SEEK_END)</span></code>: Fast-forward to the end of the stream.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">seek(0,</span> <span class="pre">SEEK_CUR)</span></code>: Leave the current stream position unchanged.</p></li>
</ul>
<p>Any other argument combinations are invalid,
and may raise exceptions.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><a class="reference internal" href="os.html#os.SEEK_SET" title="os.SEEK_SET"><code class="xref py py-data docutils literal notranslate"><span class="pre">os.SEEK_SET</span></code></a>, <a class="reference internal" href="os.html#os.SEEK_CUR" title="os.SEEK_CUR"><code class="xref py py-data docutils literal notranslate"><span class="pre">os.SEEK_CUR</span></code></a>, and <a class="reference internal" href="os.html#os.SEEK_END" title="os.SEEK_END"><code class="xref py py-data docutils literal notranslate"><span class="pre">os.SEEK_END</span></code></a>.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="io.TextIOWrapper.tell">
<span class="sig-name descname"><span class="pre">tell</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#io.TextIOWrapper.tell" title="Link to this definition">¶</a></dt>
<dd><p>Return the stream position as an opaque number.
The return value of <code class="xref py py-meth docutils literal notranslate"><span class="pre">tell()</span></code> can be given as input to <a class="reference internal" href="#io.TextIOWrapper.seek" title="io.TextIOWrapper.seek"><code class="xref py py-meth docutils literal notranslate"><span class="pre">seek()</span></code></a>,
to restore a previous stream position.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="io.StringIO">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">io.</span></span><span class="sig-name descname"><span class="pre">StringIO</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">initial_value</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">''</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">newline</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'\n'</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#io.StringIO" title="Link to this definition">¶</a></dt>
<dd><p>A text stream using an in-memory text buffer.  It inherits from
<a class="reference internal" href="#io.TextIOBase" title="io.TextIOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">TextIOBase</span></code></a>.</p>
<p>The text buffer is discarded when the <a class="reference internal" href="#io.IOBase.close" title="io.IOBase.close"><code class="xref py py-meth docutils literal notranslate"><span class="pre">close()</span></code></a> method is
called.</p>
<p>The initial value of the buffer can be set by providing <em>initial_value</em>.
If newline translation is enabled, newlines will be encoded as if by
<a class="reference internal" href="#io.TextIOBase.write" title="io.TextIOBase.write"><code class="xref py py-meth docutils literal notranslate"><span class="pre">write()</span></code></a>.  The stream is positioned at the start of the
buffer which emulates opening an existing file in a <code class="docutils literal notranslate"><span class="pre">w+</span></code> mode, making it
ready for an immediate write from the beginning or for a write that
would overwrite the initial value.  To emulate opening a file in an <code class="docutils literal notranslate"><span class="pre">a+</span></code>
mode ready for appending, use <code class="docutils literal notranslate"><span class="pre">f.seek(0,</span> <span class="pre">io.SEEK_END)</span></code> to reposition the
stream at the end of the buffer.</p>
<p>The <em>newline</em> argument works like that of <a class="reference internal" href="#io.TextIOWrapper" title="io.TextIOWrapper"><code class="xref py py-class docutils literal notranslate"><span class="pre">TextIOWrapper</span></code></a>,
except that when writing output to the stream, if <em>newline</em> is <code class="docutils literal notranslate"><span class="pre">None</span></code>,
newlines are written as <code class="docutils literal notranslate"><span class="pre">\n</span></code> on all platforms.</p>
<p><a class="reference internal" href="#io.StringIO" title="io.StringIO"><code class="xref py py-class docutils literal notranslate"><span class="pre">StringIO</span></code></a> provides this method in addition to those from
<a class="reference internal" href="#io.TextIOBase" title="io.TextIOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">TextIOBase</span></code></a> and <a class="reference internal" href="#io.IOBase" title="io.IOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">IOBase</span></code></a>:</p>
<dl class="py method">
<dt class="sig sig-object py" id="io.StringIO.getvalue">
<span class="sig-name descname"><span class="pre">getvalue</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#io.StringIO.getvalue" title="Link to this definition">¶</a></dt>
<dd><p>Return a <a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a> containing the entire contents of the buffer.
Newlines are decoded as if by <a class="reference internal" href="#io.TextIOBase.read" title="io.TextIOBase.read"><code class="xref py py-meth docutils literal notranslate"><span class="pre">read()</span></code></a>, although
the stream position is not changed.</p>
</dd></dl>

<p>Example usage:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">io</span>

<span class="n">output</span> <span class="o">=</span> <span class="n">io</span><span class="o">.</span><span class="n">StringIO</span><span class="p">()</span>
<span class="n">output</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s1">&#39;First line.</span><span class="se">\n</span><span class="s1">&#39;</span><span class="p">)</span>
<span class="nb">print</span><span class="p">(</span><span class="s1">&#39;Second line.&#39;</span><span class="p">,</span> <span class="n">file</span><span class="o">=</span><span class="n">output</span><span class="p">)</span>

<span class="c1"># Retrieve file contents -- this will be</span>
<span class="c1"># &#39;First line.\nSecond line.\n&#39;</span>
<span class="n">contents</span> <span class="o">=</span> <span class="n">output</span><span class="o">.</span><span class="n">getvalue</span><span class="p">()</span>

<span class="c1"># Close object and discard memory buffer --</span>
<span class="c1"># .getvalue() will now raise an exception.</span>
<span class="n">output</span><span class="o">.</span><span class="n">close</span><span class="p">()</span>
</pre></div>
</div>
</dd></dl>

<dl class="py class" id="index-5">
<dt class="sig sig-object py" id="io.IncrementalNewlineDecoder">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">io.</span></span><span class="sig-name descname"><span class="pre">IncrementalNewlineDecoder</span></span><a class="headerlink" href="#io.IncrementalNewlineDecoder" title="Link to this definition">¶</a></dt>
<dd><p>A helper codec that decodes newlines for <a class="reference internal" href="../glossary.html#term-universal-newlines"><span class="xref std std-term">universal newlines</span></a> mode.
It inherits from <a class="reference internal" href="codecs.html#codecs.IncrementalDecoder" title="codecs.IncrementalDecoder"><code class="xref py py-class docutils literal notranslate"><span class="pre">codecs.IncrementalDecoder</span></code></a>.</p>
</dd></dl>

</section>
</section>
<section id="performance">
<h2>Performance<a class="headerlink" href="#performance" title="Link to this heading">¶</a></h2>
<p>This section discusses the performance of the provided concrete I/O
implementations.</p>
<section id="id2">
<h3>Binary I/O<a class="headerlink" href="#id2" title="Link to this heading">¶</a></h3>
<p>By reading and writing only large chunks of data even when the user asks for a
single byte, buffered I/O hides any inefficiency in calling and executing the
operating system’s unbuffered I/O routines.  The gain depends on the OS and the
kind of I/O which is performed.  For example, on some modern OSes such as Linux,
unbuffered disk I/O can be as fast as buffered I/O.  The bottom line, however,
is that buffered I/O offers predictable performance regardless of the platform
and the backing device.  Therefore, it is almost always preferable to use
buffered I/O rather than unbuffered I/O for binary data.</p>
</section>
<section id="id3">
<h3>Text I/O<a class="headerlink" href="#id3" title="Link to this heading">¶</a></h3>
<p>Text I/O over a binary storage (such as a file) is significantly slower than
binary I/O over the same storage, because it requires conversions between
unicode and binary data using a character codec.  This can become noticeable
handling huge amounts of text data like large log files.  Also,
<a class="reference internal" href="#io.TextIOBase.tell" title="io.TextIOBase.tell"><code class="xref py py-meth docutils literal notranslate"><span class="pre">tell()</span></code></a> and <a class="reference internal" href="#io.TextIOBase.seek" title="io.TextIOBase.seek"><code class="xref py py-meth docutils literal notranslate"><span class="pre">seek()</span></code></a> are both quite slow
due to the reconstruction algorithm used.</p>
<p><a class="reference internal" href="#io.StringIO" title="io.StringIO"><code class="xref py py-class docutils literal notranslate"><span class="pre">StringIO</span></code></a>, however, is a native in-memory unicode container and will
exhibit similar speed to <a class="reference internal" href="#io.BytesIO" title="io.BytesIO"><code class="xref py py-class docutils literal notranslate"><span class="pre">BytesIO</span></code></a>.</p>
</section>
<section id="multi-threading">
<h3>Multi-threading<a class="headerlink" href="#multi-threading" title="Link to this heading">¶</a></h3>
<p><a class="reference internal" href="#io.FileIO" title="io.FileIO"><code class="xref py py-class docutils literal notranslate"><span class="pre">FileIO</span></code></a> objects are thread-safe to the extent that the operating system
calls (such as <em class="manpage"><a class="manpage reference external" href="https://manpages.debian.org/read(2)">read(2)</a></em> under Unix) they wrap are thread-safe too.</p>
<p>Binary buffered objects (instances of <a class="reference internal" href="#io.BufferedReader" title="io.BufferedReader"><code class="xref py py-class docutils literal notranslate"><span class="pre">BufferedReader</span></code></a>,
<a class="reference internal" href="#io.BufferedWriter" title="io.BufferedWriter"><code class="xref py py-class docutils literal notranslate"><span class="pre">BufferedWriter</span></code></a>, <a class="reference internal" href="#io.BufferedRandom" title="io.BufferedRandom"><code class="xref py py-class docutils literal notranslate"><span class="pre">BufferedRandom</span></code></a> and <a class="reference internal" href="#io.BufferedRWPair" title="io.BufferedRWPair"><code class="xref py py-class docutils literal notranslate"><span class="pre">BufferedRWPair</span></code></a>)
protect their internal structures using a lock; it is therefore safe to call
them from multiple threads at once.</p>
<p><a class="reference internal" href="#io.TextIOWrapper" title="io.TextIOWrapper"><code class="xref py py-class docutils literal notranslate"><span class="pre">TextIOWrapper</span></code></a> objects are not thread-safe.</p>
</section>
<section id="reentrancy">
<h3>Reentrancy<a class="headerlink" href="#reentrancy" title="Link to this heading">¶</a></h3>
<p>Binary buffered objects (instances of <a class="reference internal" href="#io.BufferedReader" title="io.BufferedReader"><code class="xref py py-class docutils literal notranslate"><span class="pre">BufferedReader</span></code></a>,
<a class="reference internal" href="#io.BufferedWriter" title="io.BufferedWriter"><code class="xref py py-class docutils literal notranslate"><span class="pre">BufferedWriter</span></code></a>, <a class="reference internal" href="#io.BufferedRandom" title="io.BufferedRandom"><code class="xref py py-class docutils literal notranslate"><span class="pre">BufferedRandom</span></code></a> and <a class="reference internal" href="#io.BufferedRWPair" title="io.BufferedRWPair"><code class="xref py py-class docutils literal notranslate"><span class="pre">BufferedRWPair</span></code></a>)
are not reentrant.  While reentrant calls will not happen in normal situations,
they can arise from doing I/O in a <a class="reference internal" href="signal.html#module-signal" title="signal: Set handlers for asynchronous events."><code class="xref py py-mod docutils literal notranslate"><span class="pre">signal</span></code></a> handler.  If a thread tries to
re-enter a buffered object which it is already accessing, a <a class="reference internal" href="exceptions.html#RuntimeError" title="RuntimeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">RuntimeError</span></code></a>
is raised.  Note this doesn’t prohibit a different thread from entering the
buffered object.</p>
<p>The above implicitly extends to text files, since the <a class="reference internal" href="functions.html#open" title="open"><code class="xref py py-func docutils literal notranslate"><span class="pre">open()</span></code></a> function
will wrap a buffered object inside a <a class="reference internal" href="#io.TextIOWrapper" title="io.TextIOWrapper"><code class="xref py py-class docutils literal notranslate"><span class="pre">TextIOWrapper</span></code></a>.  This includes
standard streams and therefore affects the built-in <a class="reference internal" href="functions.html#print" title="print"><code class="xref py py-func docutils literal notranslate"><span class="pre">print()</span></code></a> function as
well.</p>
</section>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">io</span></code> — Core tools for working with streams</a><ul>
<li><a class="reference internal" href="#overview">Overview</a><ul>
<li><a class="reference internal" href="#text-i-o">Text I/O</a></li>
<li><a class="reference internal" href="#binary-i-o">Binary I/O</a></li>
<li><a class="reference internal" href="#raw-i-o">Raw I/O</a></li>
</ul>
</li>
<li><a class="reference internal" href="#text-encoding">Text Encoding</a><ul>
<li><a class="reference internal" href="#opt-in-encodingwarning">Opt-in EncodingWarning</a></li>
</ul>
</li>
<li><a class="reference internal" href="#high-level-module-interface">High-level Module Interface</a></li>
<li><a class="reference internal" href="#class-hierarchy">Class hierarchy</a><ul>
<li><a class="reference internal" href="#i-o-base-classes">I/O Base Classes</a></li>
<li><a class="reference internal" href="#raw-file-i-o">Raw File I/O</a></li>
<li><a class="reference internal" href="#buffered-streams">Buffered Streams</a></li>
<li><a class="reference internal" href="#id1">Text I/O</a></li>
</ul>
</li>
<li><a class="reference internal" href="#performance">Performance</a><ul>
<li><a class="reference internal" href="#id2">Binary I/O</a></li>
<li><a class="reference internal" href="#id3">Text I/O</a></li>
<li><a class="reference internal" href="#multi-threading">Multi-threading</a></li>
<li><a class="reference internal" href="#reentrancy">Reentrancy</a></li>
</ul>
</li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="os.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">os</span></code> — Miscellaneous operating system interfaces</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="time.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">time</span></code> — Time access and conversions</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/io.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="time.html" title="time — Time access and conversions"
             >next</a> |</li>
        <li class="right" >
          <a href="os.html" title="os — Miscellaneous operating system interfaces"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="allos.html" >Generic Operating System Services</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">io</span></code> — Core tools for working with streams</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>